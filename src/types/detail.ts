export interface DetailTab {
    title: string;
    key: string;
    component?: string;
}

export interface BaseDetailData {
    id: string | number;
    name: string;
    description?: string;
    logoUrl?: string;
    source?: number;
    modifiedStime?: string;
}

export interface ServerDetailData extends BaseDetailData {
    programLanguage?: string;
    supplier?: string;
    gitUrl?: string;
    introduction?: string;
    tags?: string[];
}

export interface ClientDetailData extends BaseDetailData {
    version?: string;
    author?: string;
    repository?: string;
    documentation?: string;
}

export interface UseCaseDetailData extends BaseDetailData {
    category?: string;
    author?: string;
    publishTime?: string;
    content?: string;
}

export type DetailType = 'server' | 'client' | 'usecase';
