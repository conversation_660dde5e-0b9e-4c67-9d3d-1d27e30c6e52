@import '@/primeFlex.css';
@import '@/normalize.css';
@import '@/assets/styles/iphoneColor.scss';

html,
body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* 禁止滑动 */
    margin: 0;
    padding: 0;
    background-color: #fff;
    font-size: 16px;
}
:root{
    --primary-color: #005aff;
}


#app {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

div {
    box-sizing: border-box;
}

.dlrContainer {
    width: 100%;
    height: 100%;
    min-height: 100%;
    box-sizing: border-box;

    &-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(180deg, rgba($system-teal, 0.1), #fff 380px);
        &-scroll {
            flex: 1;
            height: 100%;
            overflow: hidden;
            overflow-y: auto;
            position: relative;
            &.dlrContainer-wrapper-scroll-no-padding{
                width: 100%;
                max-width: 100%;
            }
            &::-webkit-scrollbar {
                display: none;
            }
        }
    }
}

/*覆盖默认的样式*/
.custom-tooltip {
    font-size: 14px !important;
    max-width: none;
}

// PC端最小宽度
@media (max-width: 1279px) {
  html, body, #app {
    width: 1280px;
    overflow-x: auto;
  }
  .dlrContainer-wrapper-scroll {
    width: 1280px;
  }
}

// 大屏（1920及以上）
@media (min-width: 1920px) {
  .dlrContainer-wrapper-scroll {
    width: 1760px;
    margin: 0 auto;
  }
}

// 中等屏幕（1440-1919）
@media (min-width: 1440px) and (max-width: 1919px) {
  .dlrContainer-wrapper-scroll {
    width: 1440px;
    margin: 0 auto;
  }
}

// 小屏（1280-1439）
@media (min-width: 1280px) and (max-width: 1439px) {
  .dlrContainer-wrapper-scroll {
    width: 1280px;
    margin: 0 auto;
  }
}

// 超大屏（2560及以上）
@media (min-width: 2560px) {
  .dlrContainer-wrapper-scroll {
    width: 1920px;
    margin: 0 auto;
  }
}


//通用卡片样式

.type-tag {
    font-size: 11px;
    height: 18px;
    line-height: 18px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    min-width: 32px;
    justify-content: center;
    margin-left: 6px;
    flex-shrink: 0;
    white-space: nowrap;
}
.pb20{
    padding-bottom: 1rem;
}
.no-data-container{
    padding-top: 3rem;
}
.server-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--surface-200);
    cursor: pointer;
    background: #fff;

    &:hover {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }
    .card-header {
        display: flex;
        align-items: center;
        padding: .8em 1rem 0;
    }

    .server-title {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 0 0.5rem;
        width: 100%;

        .name {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .supplier {
            font-size: .8rem;
            color: var(--text-color-secondary);
            margin: 0;
            line-height: 1.5;
            margin-top: 0.2rem;
        }

        &-left {
            flex: 1;
            width: 1%;
        }

        .server-logo {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            overflow: hidden;
            margin: 0;
            padding: 0;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .card-header {
            padding: 1.25rem;
            background: linear-gradient(135deg,
                    var(--primary-color-lighter, #f0f7ff) 0%,
                    var(--surface-100) 100%);
            border-bottom: 1px solid var(--surface-200);
            height: 48px;
            display: flex;
            align-items: center;
        }

        .card-content {
            padding: 0 1rem;
            text-overflow: ellipsis;
        }

    }

    .card-content {
        padding: 0 1rem;
        flex: 1;
        gap: 0.5rem;
    }

    .language-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid var(--surface-200);
        padding: 0.25rem 0;
        min-height: 48px;
        box-sizing: border-box;
        margin-top: 0.5rem;

        .language {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            font-size: 0.75rem;
            color: var(--text-color-secondary);

            .language-dot {
                width: 0.5rem;
                height: 0.5rem;
                border-radius: 50%;
                margin-right: 0.25rem;
            }

            :deep(.p-button) {
                padding: 0.25rem;
                width: 1.75rem;
                height: 1.75rem;

                .p-button-icon {
                    font-size: 0.875rem;
                }
            }
        }

        :deep(.p-button) {
            width: 1.75rem;
            height: 1.75rem;

            .p-button-icon {
                font-size: 0.875rem;
            }
        }
    }

    .card-description {
        min-height: 0;
        padding-bottom: 0.5rem;
        padding-top: 0.5rem;

        p {
            margin: 0;
            font-size: 0.75rem;
            line-height: 1.5;
            height: 2.25rem;
            color: var(--text-color-tertiary);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-all;
        }
    }

    .card-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 0.375rem;
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        margin-top: 0.5rem;
        line-height: 1.5;

        .language-dot {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
        }
    }

    .card-meta {
        .meta-stats {
            display: flex;
            gap: 0.75rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            i {
                font-size: 0.75rem;
            }

            &.downloads i {
                color: var(--blue-500);
            }

            &.stars i {
                color: var(--yellow-500);
            }
        }
    }
}


.p-tag.p-tag-info{
    background: var(--primary-color);
    color: #fff;
}

.platform-icons {
    display: flex;
    gap: 8px;
}

.platform-icon img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
}
.client-type-tag{
    font-size: .6rem;
    color: var(--text-color-secondary);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: #e3f0ff;
    color: #1976d2;
    border-radius: 2px;
    padding: 0 4px;
    margin-right: 4px;
    line-height: 1.4;
    font-weight: bold;
}

.git-repo {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }
}

.git-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.p-paginator{
    background: rgba(0, 0, 0, 0);
}