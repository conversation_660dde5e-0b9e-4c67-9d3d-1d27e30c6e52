<template>
    <Toast
        position="top-center"
        :pt="{
            summary: {
                style: {
                    fontSize: '14px',
                    fontWeight: 'bold',
                },
            },
            detail: {
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                    lineHeight: '1.5',
                    letterSpacing: '0.4px',
                },
            },
        }"
    />
    <component :is="layout">
        <router-view />
    </component>
</template>
<script setup>
import { RouterView } from 'vue-router';
import DefaultLayout from '@/layouts/default-layout.vue';
import BlankLayout from '@/layouts/blank-layout.vue';
import Toast from 'primevue/toast';
import { computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { toastEventBus } from '@/utils/toast';

const route = useRoute();
const toast = useToast();

const layout = computed(() => {
    const layoutName = route.meta.layout || 'default';
    return layoutName === 'blank' ? BlankLayout : DefaultLayout;
});

// 监听 toast 事件
onMounted(() => {
    toastEventBus.on((message) => {
        toast.add(message);
    });
});
</script>
