<template>
    <div class="dlrContainer-wrapper">
        <top-bar />
        <div
            class="dlrContainer-wrapper-scroll"
            :class="{
                'dlrContainer-wrapper-scroll-no-padding': noPaddingPages.includes($route.path)
            }"
        >
            <slot />
        </div>
    </div>
</template>

<script setup>
import TopBar from '@/components/layout/top-bar.vue';
// import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
console.log(route);
// 定义需要移除 padding 的页面路径
const noPaddingPages = [
    '/document',
    '/playground',
    '/inspector',
    '/ide-plugins'
    // 在这里添加更多需要 no-padding 的页面路径
];
</script>
