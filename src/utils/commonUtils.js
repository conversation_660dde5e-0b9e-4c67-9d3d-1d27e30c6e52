/**
 * 通用工具函数
 */
import {debounce as _debounce, throttle as _throttle} from 'lodash';
import dayjs from 'dayjs';

/**
 * Git仓库类型枚举
 */
export const GitRepoType = {
    GITHUB: 'github',
    CORP: 'corp',
};

/**
 * 获取Git仓库类型
 * @param {string} url Git仓库URL
 * @returns {string} 返回仓库类型
 */
export const getGitRepoType = (url) => {
    if (!url) {
        return '';
    }

    try {
        const urlObj = new URL(url);
        if (urlObj.hostname === 'github.com') {
            return GitRepoType.GITHUB;
        } else if (urlObj.hostname === 'git.corpautohome.com') {
            return GitRepoType.CORP;
        }
        return '';
    } catch (e) {
        return '';
    }
};

/**
 * 获取Git仓库类型
 * @param {string} url Git仓库URL
 * @returns {{ type: string, icon: string }} 返回仓库类型和对应的图标
 */
// export const getGitRepoInfo = (url) => {
//     if (!url) {
//         return { type: GitRepoType.UNKNOWN, icon: 'pi pi-link' };
//     }

//     try {
//         const urlObj = new URL(url);
//         if (urlObj.hostname === 'github.com') {
//             return {
//                 type: GitRepoType.GITHUB,
//                 icon: 'pi pi-github',
//             };
//         } else if (urlObj.hostname === 'git.corpautohome.com') {
//             return {
//                 type: GitRepoType.CORP,
//                 icon: 'pi pi-gitlab',
//             };
//         }
//         return { type: GitRepoType.UNKNOWN, icon: 'pi pi-code' };
//     } catch (e) {
//         return { type: GitRepoType.UNKNOWN, icon: 'pi pi-code' };
//     }
// };

/**
 * 防抖函数 - 延迟执行函数直到停止触发一段时间后
 * @param {Function} fn 要执行的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {Object} options 选项对象
 * @param {boolean} options.leading 是否在延迟开始前调用函数
 * @param {boolean} options.trailing 是否在延迟结束后调用函数
 * @returns {Function} 防抖后的函数
 */
export const debounce = (fn, wait = 300, options = {}) => {
    return _debounce(fn, wait, options);
};

/**
 * 节流函数 - 限制函数在一定时间内执行的频率
 * @param {Function} fn 要执行的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {Object} options 选项对象
 * @param {boolean} options.leading 是否在节流开始前调用函数
 * @param {boolean} options.trailing 是否在节流结束后调用函数
 * @returns {Function} 节流后的函数
 */
export const throttle = (fn, wait = 300, options = {}) => {
    return _throttle(fn, wait, options);
};

// 格式化日期时间
export const formatDateTime = (dateTime) => {
    if (!dateTime) {return '';}
    return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 格式化日期为中文格式（年月日）
 * @param {string} dateStr 日期字符串
 * @returns {string} 格式化后的日期字符串，格式：XXXX年XX月XX日
 */
export const formatDate = (dateStr) => {
    if (!dateStr) {
        return '';
    }

    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr;
        }

        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    } catch (e) {
        return dateStr;
    }
};
