/**
 * 通用表单验证规则库
 */
export const validators = {
    /**
     * 必填验证器
     */
    required: {
        check: (value) => {
            if (typeof value === 'number') {return true;} // 数字类型（包括0）都视为有效
            if (typeof value === 'string') {return value.trim().length > 0;} // 字符串必须非空
            return !!value; // 其他类型使用原有的检查方式
        },
        message: '此项为必填项',
    },

    /**
     * URL格式验证器
     */
    url: {
        check: (value) => {
            if (!value) {return true;}
            try {
                new URL(value);
                return true;
            } catch {
                return false;
            }
        },
        message: '请输入有效的URL',
    },

    /**
     * 电子邮件验证器
     */
    email: {
        check: (value) => {
            if (!value) {return true;}
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailPattern.test(value);
        },
        message: '请输入有效的电子邮件地址',
    },

    /**
     * 最小长度验证器
     * @param {number} length 最小长度
     * @returns {Object} 验证器对象
     */
    minLength: (length) => ({
        check: (value) => !value || value.length >= length,
        message: `最少需要 ${length} 个字符`,
    }),

    /**
     * 最大长度验证器
     * @param {number} length 最大长度
     * @returns {Object} 验证器对象
     */
    maxLength: (length) => ({
        check: (value) => !value || value.length <= length,
        message: `最多允许 ${length} 个字符`,
    }),

    /**
     * GitHub仓库URL验证器
     */
    githubUrl: {
        check: (value) => {
            if (!value) {return true;}
            return /^(https?:\/\/(www\.)?(github\.com|git\.corpautohome\.com)\/[\w-]+\/[\w.-]+\/?(.*))$/.test(
                value,
            );
        },
        message: '请输入有效的Git仓库地址',
    },
};
