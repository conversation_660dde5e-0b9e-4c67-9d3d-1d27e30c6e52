/**
 * toast 工具类，用于在非组件中显示 toast 消息
 */

// toast 事件总线
const toastEventBus = {
    listeners: [],
    on(callback) {
        this.listeners.push(callback);
    },
    emit(message) {
        this.listeners.forEach((callback) => callback(message));
    },
};

// 封装的 toast 方法
const toast = {
    error(message, summary = '请求失败', life = 5000) {
        toastEventBus.emit({
            severity: 'error',
            summary,
            detail: message,
            life,
        });
    },
    success(message, summary = '请求成功', life = 5000) {
        toastEventBus.emit({
            severity: 'success',
            summary,
            detail: message,
            life,
        });
    },
    info(message, summary = '温馨提示', life = 5000) {
        toastEventBus.emit({
            severity: 'info',
            summary,
            detail: message,
            life,
        });
    },
    warn(message, summary = '警告', life = 5000) {
        toastEventBus.emit({
            severity: 'warn',
            summary,
            detail: message,
            life,
        });
    },
};

export {toast, toastEventBus};
