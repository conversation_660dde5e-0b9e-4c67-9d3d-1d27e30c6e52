import { toast } from './toast';

async function get(url, params = null, headers = {}, options = {}) {
    const requestOptions = {
        url,
        method: 'GET',
        params,
        headers,
        ...options
    };
    return await request(requestOptions);
}

async function post(url, body = {}, params = null, headers = {}, options = {}) {
    const isFormData = body instanceof FormData;
    const shouldSendJson = !isFormData;
    const requestOptions = {
        url,
        method: 'POST',
        body: body,
        params,
        headers: {
            ...(shouldSendJson && {
                'Content-Type': 'application/json'
            }),
            ...headers
        },
        ...options
    };
    return await request(requestOptions);
}

async function put(url, body = {}, headers = {}, options = {}) {
    const requestOptions = {
        url,
        method: 'PUT',
        body,
        headers,
        ...options
    };
    return await request(requestOptions);
}

async function deleteRequest(url, body = {}, headers = {}, options = {}) {
    const requestOptions = {
        url,
        method: 'DELETE',
        body,
        headers,
        ...options
    };
    return await request(requestOptions);
}

async function request(options) {
    let _url = options.url;
    const _options = {
        method: options.method || 'GET',
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    };
    if (options.params) {
        _url += '?' + new URLSearchParams(options.params).toString();
    }
    if (options.body) {
        _options.body = JSON.stringify(options.body);
    }

    // 默认显示错误提示，除非明确设置为 false
    const showErrorToast = options.showErrorToast !== false;

    try {
        const response = await fetch(_url, _options);

        // 检查 HTTP 403 状态码
        if (response.status === 510) {
            console.log('检测到 510 状态码 user_name 过期 @周晓明，执行页面刷新');
            location.reload();
            return;
        }

        const data = await response.json();

        // 检查业务返回码 403
        if (Number(data.returncode) === 403) {
            console.log('检测到 returncode 为 403，执行页面刷新');
            location.reload();
            return;
        }

        if (response.ok) {
            // 非经销商标准协议
            if (data.status && data.status !== 'Success') {
                throw new Error(data.message);
            }
            // 经销商标准协议
            if (data.returncode && data.returncode !== 0) {
                throw new Error(data.message);
            }
        } else {
            throw new Error(data.message);
        }
        return data;
    } catch (err) {
        console.log('err', err);
        // 仅在 showErrorToast 为 true 时显示错误提示
        if (showErrorToast) {
            toast.error(err.message || '请求出错');
        }
        throw err;
    }
}

export default {
    get,
    post,
    put,
    delete: deleteRequest // 注意：delete 是保留字，需要别名导出
};
