import {defineStore} from 'pinia';
import httpService from '@/utils/httpService';

export const useHomeStore = defineStore('home', {
    state: () => ({
        homeData: {},
        loading: true,
        username: '',
        keyword: '',
        source: null,
        lastRequestId: 0,
    }),
    getters: {},
    actions: {
        /**
         * 获取用户名
         * @returns {Promise<Object>} 用户名
         */
        async getUsername() {
            const url = '/sso/username';
            try {
                // 尝试手动获取并处理响应，而不依赖httpService的JSON处理
                const response = await fetch(url);
                // 判断响应状态
                if (response.ok) {
                    const data = await response.json();
                    this.username = data?.username;
                    return this.username;
                }
            } catch (err) {
                this.username = 'zhaixiaowei';
                return this.username;
            }
        },

        logout() {
            const baseUrl = window.location.origin;
            const url = `${baseUrl}/sso/logout`;
            window.location.href = url;
        },
        /**
         * 获取全局数据
         * @returns {Promise<Object>} 全局数据
         */
        async globalSearch(params) {
            this.loading = true;
            const requestId = ++this.lastRequestId;
            const url = '/api/firstPage/globalSearch';
            try {
                const data = await httpService.get(url, params);
                if (requestId === this.lastRequestId) {
                    this.homeData = data.result;
                }
                return data;
            } catch (error) {
                if (requestId === this.lastRequestId) {
                    this.homeData = {};
                }
                throw error;
            } finally {
                if (requestId === this.lastRequestId) {
                    this.loading = false;
                }
            }
        },
        /**
         * 设置搜索关键词
         * @param {string} keyword 搜索关键词
         */
        setKeyword(keyword) {
            this.keyword = keyword;
        },
        /**
         * 清除搜索关键词
         */
        clearKeyword() {
            this.keyword = '';
        },
        /**
         * 设置来源类型
         * @param {number} source 来源类型
         */
        setSource(source) {
            this.source = source;
        },
        /**
         * 清除来源类型
         */
        clearSource() {
            this.source = null;
        },
    },
});
