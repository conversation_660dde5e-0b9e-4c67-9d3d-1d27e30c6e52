import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';

export const useApiKeysStore = defineStore('apiKeys', {
    state: () => ({
        // API密钥列表
        apiKeysList: [],
        // 加载状态
        loading: false,
        // 错误信息
        error: null
    }),

    actions: {
        /**
         * 获取API密钥列表
         * @param {Object} params - 查询参数
         * @returns {Promise<Object>} 返回API响应结果
         */
        async getApiKeys(params = {}) {
            try {
                this.loading = true;
                this.error = null;

                const url = '/llm-api/dev/getApiKey';
                const response = await httpService.get(url, params);

                // 检查响应结构
                if (response && response.returncode === 0) {
                    this.apiKeysList = response.result || [];
                    return response;
                } else {
                    const errorMessage = response?.message || '获取API密钥列表失败';
                    this.error = errorMessage;
                    throw new Error(errorMessage);
                }
            } catch (error) {
                this.error = error.message || '网络请求失败';
                console.error('获取API密钥列表失败:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 清空数据
         */
        clearData() {
            this.apiKeysList = [];
            this.error = null;
        }
    }
});
