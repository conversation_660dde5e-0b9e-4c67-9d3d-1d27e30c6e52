import {defineStore} from 'pinia';
import httpService from '@/utils/httpService';

export const useSubmitStore = defineStore('submit', {
    state: () => ({
        repoData: {},
    }),
    getters: {},
    actions: {
        /**
         * 获取仓库详情
         * @param {Object} params - 请求参数
         * @param {string} params.gitUrl - 仓库URL
         * @returns {Promise<Object>} 仓库详情
         */
        async getRepoDetail(params) {
            const url = '/api/git/repo/detail';
            try {
                const data = await httpService.get(url, params, {}, {showErrorToast: false});

                if (data.result) {
                    this.repoData = data.result;
                }
            } catch (error) {
                console.error('获取仓库详情失败', error);
                throw error;
            }
        },

        /**
         * 清空仓库数据
         */
        clearRepoData() {
            this.repoData = {};
        },
    },
});
