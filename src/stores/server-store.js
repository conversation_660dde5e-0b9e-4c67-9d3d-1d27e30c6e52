import {defineStore} from 'pinia';
import httpService from '@/utils/httpService';

export const useServerStore = defineStore('server', {
    state: () => ({
        serversList: [],
        serverDetail: {},
        serverCategoryAndCounts: [],
        loading: false,
        error: null,
        currentPage: 1,
        pageSize: 10,
        hasMore: false,
        lastRequestId: 0,
        totalRecords: 0,
    }),

    getters: {
        /**
         * 是否有服务器数据
         */
        hasServers: (state) => state.serversList.length > 0,

        /**
         * 是否有更多数据可加载
         */
        canLoadMore: (state) => state.hasMore && !state.loading,
    },

    actions: {
        /**
         * 获取服务器列表
         * @param {Object} params - 查询参数
         * @param {number} params.pageIndex - 页码
         * @param {number} params.pageSize - 每页数量
         * @param {string} params.keyword - 搜索关键词
         * @param {number} params.sortType - 排序类型
         * @param {number} params.source - 来源类型
         * @param {number} params.serverCategoryId - 分类ID
         * @returns {Promise<Object>} 返回服务器列表数据
         */
        async getServers(params = {}) {
            const {pageIndex = 1, pageSize = 20} = params;
            const requestId = ++this.lastRequestId;

            try {
                this.loading = true;
                this.error = null;

                const response = await httpService.get('/api/server/query', params);

                // 统一处理响应数据格式
                const result = this._normalizeServerResponse(response);

                // 防止请求竞态条件
                if (requestId === this.lastRequestId) {
                    this._updateServerState(result, pageIndex, pageSize);
                }

                return result;
            } catch (error) {
                if (requestId === this.lastRequestId) {
                    this._handleServerError(error);
                }
                throw error;
            } finally {
                if (requestId === this.lastRequestId) {
                    this.loading = false;
                }
            }
        },

        /**
         * 获取服务器详情
         * @param {string|number} id - 服务器ID
         * @returns {Promise<Object>} 服务器详情数据
         */
        async getServerDetail(id) {
            if (!id) {
                throw new Error('服务器ID不能为空');
            }

            try {
                this.loading = true;
                this.error = null;

                const response = await httpService.get(`/api/server/detail?id=${id}`);

                if (response?.result) {
                    this.serverDetail = response.result;
                    return response;
                } else {
                    throw new Error('服务器详情数据格式错误');
                }
            } catch (error) {
                this.error = error.message || '获取服务器详情失败';
                console.error('获取服务器详情失败:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 清空服务器列表
         */
        clearServers() {
            this.serversList = [];
            this.totalRecords = 0;
            this.hasMore = false;
            this.currentPage = 1;
        },

        /**
         * 重置错误状态
         */
        clearError() {
            this.error = null;
        },

        /**
         * 标准化服务器响应数据
         * @private
         */
        _normalizeServerResponse(response) {
            if (response?.result) {
                return response.result;
            } else if (Array.isArray(response)) {
                return {
                    list: response.list || [],
                    rowcount: response.rowcount || 0,
                    serverCategoryAndCounts: response.serverCategoryAndCounts || [],
                };
            } else {
                return {
                    list: [],
                    rowcount: 0,
                    serverCategoryAndCounts: [],
                };
            }
        },

        /**
         * 更新服务器状态
         * @private
         */
        _updateServerState(result, pageIndex, pageSize) {
            this.serversList = result.list || [];
            this.hasMore = (result.list?.length || 0) === pageSize;
            this.currentPage = pageIndex;
            this.pageSize = pageSize;
            this.totalRecords = result.rowcount ?? 0;
            this.serverCategoryAndCounts = result.serverCategoryAndCounts ?? [];
        },

        /**
         * 处理服务器请求错误
         * @private
         */
        _handleServerError(error) {
            this.error = error.message || '获取数据失败';
            this.serversList = [];
            this.serverCategoryAndCounts = [];
        },
    },
});
