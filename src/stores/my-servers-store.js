import {defineStore} from 'pinia';
import httpService from '@/utils/httpService';

export const useMyServersStore = defineStore('my-servers', {
    state: () => ({
        serverCategories: [],
        myServerData: {},
        serverDetail: {},
    }),
    getters: {},
    actions: {
        /**
         * 获取仓库详情
         * @returns {Promise<Object>} 服务器类别
         */
        async getServerCategories() {
            const url = '/api/server/categories';
            try {
                const data = await httpService.get(url);
                this.serverCategories = data.result;
                return data;
            } catch (error) {
                console.error('获取服务器类别失败', error);
                throw error;
            }
        },

        /**
         * 添加MCP服务器
         * @param {Object} body - 表单项目
         * @returns {Promise<Object>}
         */
        async addMcpServer(body) {
            const url = '/api/server/add';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (error) {
                console.error('添加MCP服务器失败', error);
                throw error;
            }
        },

        /**
         * 更新MCP服务器
         * @param {Object} body - 表单项目
         * @returns {Promise<Object>}
         */
        async updateMcpServer(body) {
            const url = '/api/server/update';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (error) {
                console.error('更新MCP服务器失败', error);
                throw error;
            }
        },

        /**
         * 获取 MCP Server 列表
         * @returns {Promise<Object>}
         */
        async getMcpServerList(params) {
            const url = '/api/server/query';
            try {
                const data = await httpService.get(url, params);
                this.myServerData = data.result;
                return data;
            } catch (error) {
                console.error('获取MCP服务器列表失败', error);
                throw error;
            }
        },

        /**
         * 获取MCP服务器详情
         * @param {Object} params - 请求参数
         * @returns {Promise<Object>}
         */
        async getMcpServerDetail(params) {
            const url = '/api/server/detail';
            try {
                const data = await httpService.get(url, params);
                this.serverDetail = data.result;
                return data;
            } catch (error) {
                console.error('获取MCP服务器详情失败', error);
                throw error;
            }
        },
    },
});
