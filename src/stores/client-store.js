import {defineStore} from 'pinia';
import httpService from '@/utils/httpService';

export const useClientStore = defineStore('client', {
    state: () => ({
        clientsList: [],
        clientDetail: {},
        loading: false,
        error: null,
        currentPage: 1,
        pageSize: 10,
        hasMore: false,
        lastRequestId: 0,
        totalRecords: 0,
    }),
    actions: {
        async getClients(params) {
            const {
                pageIndex = 1,
                pageSize = 20,
            } = params;
            const requestId = ++this.lastRequestId;
            try {
                this.loading = true;
                this.error = null;
                const url = '/api/client/query';
                const response = await httpService.get(url, params);
                let result = {};
                if (response && response.result) {
                    result = response.result;
                } else if (Array.isArray(response)) {
                    result = {list: response, rowcount: response.length};
                } else {
                    result = {list: [], rowcount: 0};
                }
                if (requestId === this.lastRequestId) {
                    this.clientsList = result.list || [];
                    this.hasMore = (result.list?.length || 0) === pageSize;
                    this.currentPage = pageIndex;
                    this.pageSize = pageSize;
                    this.totalRecords = result.rowcount ?? 100;
                }
                return result;
            } catch (error) {
                if (requestId === this.lastRequestId) {
                    this.error = error.message || '获取数据失败';
                    this.clientsList = [];
                }
                return {list: [], rowcount: 0};
            } finally {
                if (requestId === this.lastRequestId) {
                    this.loading = false;
                }
            }
        },
        async getClientDetail(id) {
            try {
                const url = `/api/client/detail?id=${id}`;
                const response = await httpService.get(url);
                console.log('客户端列表API返回数据:', response);
                this.clientDetail = response;

                return this.clientDetail;
            } catch (error) {
                console.error('获取客户端详情失败:', error);
                return error;
            } finally {
                // this.loading = false;
            }
        },
    },
});
