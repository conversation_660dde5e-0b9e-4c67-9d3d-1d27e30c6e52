import { createRouter, createWebHistory } from 'vue-router';

const routes = [
    // 首页
    {
        path: '/',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
            layout: 'default'
        }
    },
    // 《详情》页面

    // 《我的》 submit - 提交 mcp server页面
    {
        path: '/submit',
        name: 'Submit',
        component: () => import('@/views/my-servers/submit.vue'),
        meta: {
            layout: 'default'
        },
        children: []
    },
    // "编辑Server"页面
    {
        path: '/server-edit',
        name: 'ServerEdit',
        component: () => import('@/views/my-servers/edit.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 《我的 -->   MCP Server》页面
    {
        path: '/my-servers',
        name: 'MyServers',
        component: () => import('@/views/my-servers/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    {
        path: '/my-apikeys',
        name: 'Api<PERSON><PERSON><PERSON>',
        component: () => import('@/views/my-apikeys/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    // Servers列表页面
    {
        path: '/servers',
        name: 'serverList',
        component: () => import('@/views/servers/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    {
        path: '/ide-plugins',
        name: 'idePlugins',
        component: () => import('@/views/ide-plugins/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    {
        path: '/mcp-gateway',
        name: 'mcpGateway',
        component: () => import('@/views/mcp-gateway/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 服务器详情页面
    {
        path: '/servers/detail/:id',
        name: 'Detail',
        component: () => import('@/views/servers/detail.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 客户端列表页面
    {
        path: '/clients',
        name: 'ClientList',
        component: () => import('@/views/clients/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 客户端详情页面
    {
        path: '/clients/detail/:id',
        name: 'ClientDetail',
        component: () => import('@/views/clients/detail.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 使用案例
    {
        path: '/case-studies',
        name: 'usecaseList',
        component: () => import('@/views/case-studies/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 文档
    {
        path: '/document',
        name: 'document',
        component: () => import('@/views/document/index.vue')
    },
    // 在线试用
    {
        path: '/playground',
        name: 'playground',
        component: () => import('@/views/playground/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 在线调试
    {
        path: '/inspector',
        name: 'inspector',
        component: () => import('@/views/inspector/index.vue'),
        meta: {
            layout: 'default'
        }
    },
    // 创新大赛活动页面
    {
        path: '/innovation-competition',
        name: 'InnovationCompetition',
        component: () => import('@/views/innovation-competition/index.vue'),
        meta: {
            layout: 'blank'
        }
    }
];
const router = createRouter({
    history: createWebHistory(),
    routes
});

export default router;
