// src/enums/language.js

export const Language = {
    JavaScript: 'JavaScript',
    TypeScript: 'TypeScript',
    Python: 'Python',
    Java: 'Java',
    Go: 'Go',
    Rust: 'Rust',
    CSharp: 'C#',
    PHP: 'PHP',
    Ruby: 'Ruby',
    Node: 'Node',
};

export const LanguageColorMap = {
    [Language.JavaScript]: '#f7df1e', // 明亮黄
    [Language.TypeScript]: '#3178c6', // 亮蓝
    [Language.Python]: '#3776ab', // 深蓝
    [Language.Java]: '#e76f00', // 橙色
    [Language.Go]: '#00add8', // 青色
    [Language.Rust]: '#b7410e', // 棕红
    [Language.CSharp]: '#68217a', // 紫色
    [Language.PHP]: '#8993be', // 紫灰
    [Language.Ruby]: '#cc342d', // 鲜红
    [Language.Node]: '#3c873a', // 绿色
};

export function getLanguageColor(language) {
    return LanguageColorMap[language] || '#858585';
}
