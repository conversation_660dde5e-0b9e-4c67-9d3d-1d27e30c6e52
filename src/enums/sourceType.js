/**
 * 来源类型枚举
 */
export const SourceType = {
    HOME: 1, // 汽车之家
    OFFICIAL: 2, // 官方
    THIRD_PARTY: 3, // 第三方
    Severity: {
        1: 'info', // 汽车之家
        2: 'success', // 官方
        3: 'contrast', // 第三方
    },
};

/**
 * 获取来源类型名称
 * @param {number} sourceType 来源类型值
 * @returns {string} 来源类型名称
 */
export const getSourceTypeName = (sourceType) => {
    switch (Number(sourceType)) {
    case SourceType.HOME:
        return '汽车之家';
    case SourceType.OFFICIAL:
        return '官方';
    case SourceType.THIRD_PARTY:
        return '第三方';
    default:
        return '';
    }
};

/**
 * 获取来源对应的严重程度
 * @param {number} source - 来源类型
 * @returns {string} 对应的严重程度
 */
export const getSourceSeverity = (source) => {
    return SourceType.Severity[source] || 'primary';
};

/**
 * 根据枚举类型和值获取对应名称
 * @param {string} enumType 枚举类型
 * @param {number|string} value 枚举值
 * @returns {string} 枚举名称
 */
export const getEnumName = (enumType, value) => {
    if (!value && value !== 0) {return '';}

    switch (enumType) {
    case 'SourceType':
        return getSourceTypeName(value);
    default:
        return '';
    }
};
