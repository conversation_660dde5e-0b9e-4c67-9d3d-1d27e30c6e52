export const PlatformIconMap = {
    WINDOWS: 'https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/windows.png',
    MACOS: 'https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/macos.png',
    LINUX: 'https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/linux.png',
    // 可根据实际平台补充
};

export function getPlatformIcon(platform) {
    return PlatformIconMap[platform] || '';
}

/**
 * 通用平台字符串/数组解析
 * @param {string|array} input 平台字符串（逗号分隔）或数组
 * @returns {string[]} 统一大写的数组
 */
export function parsePlatformList(input) {
    if (!input) {
        return [];
    }
    if (Array.isArray(input)) {
        return input.map((p) => String(p).trim().toUpperCase()).filter(Boolean);
    }
    return String(input)
        .split(',')
        .map((p) => p.trim().toUpperCase())
        .filter(Boolean);
}
