import {reactive, ref} from 'vue';
import {toast} from '@/utils/toast';

/**
 * 通用表单处理Hook
 * @param {Object} options - 配置选项
 * @param {Array} options.fields - 字段定义数组
 * @param {Function} options.submitHandler - 提交处理函数
 * @param {Object} options.initialValues - 初始值对象
 * @returns {Object} 表单状态和方法
 */
export function useForm(options) {
    const {
        fields, // 字段定义数组
        submitHandler, // 提交处理函数
        initialValues = {}, // 初始值
    } = options;

    // 创建表单数据对象
    const formData = reactive({});

    // 创建错误状态对象
    const errors = reactive({});

    // 加载状态
    const loading = ref(false);

    // 初始化表单字段和默认值
    fields.forEach((field) => {
        formData[field.name] = initialValues[field.name] || field.defaultValue;
        errors[field.name] = false;
    });

    // 验证单个字段
    const validateField = (fieldName) => {
        const field = fields.find((f) => f.name === fieldName);
        if (!field || !field.validators) {return true;}

        for (const validator of field.validators) {
            const isValid = validator.check(formData[fieldName], formData);
            if (!isValid) {
                errors[fieldName] = validator.message;
                return false;
            }
        }

        errors[fieldName] = false;
        return true;
    };

    // 验证整个表单
    const validate = () => {
        let isValid = true;
        let firstErrorField = null;

        fields.forEach((field) => {
            if (field.validators && !validateField(field.name)) {
                isValid = false;
                if (!firstErrorField) {
                    firstErrorField = field;
                }
            }
        });

        // 如果表单验证失败，显示 toast 提示，包含未通过验证的字段名称
        if (!isValid && firstErrorField) {
            const fieldLabel = firstErrorField.label || firstErrorField.name;
            toast.error(`${errors[firstErrorField.name]}`, `${fieldLabel} : 验证未通过`);
        }

        return isValid;
    };

    // 提交表单
    const submit = async () => {
        if (!validate()) {return;}

        loading.value = true;
        try {
            await submitHandler(formData);
        } catch (error) {
            console.error('表单提交错误:', error);
        } finally {
            loading.value = false;
        }
    };

    // 重置表单
    const reset = () => {
        fields.forEach((field) => {
            formData[field.name] = initialValues[field.name] || field.defaultValue;
            errors[field.name] = false;
        });
    };

    return {
        formData,
        errors,
        loading,
        validate,
        validateField,
        submit,
        reset,
    };
}
