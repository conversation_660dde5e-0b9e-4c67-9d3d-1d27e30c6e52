import { useRouter } from 'vue-router';

/**
 * 文档导航相关的 composable
 * 提供统一的文档跳转功能
 */
export const useDocumentNavigation = () => {
    const router = useRouter();

    /**
     * 跳转到文档页面
     * @param {string} page - 文档页面路径
     * @param {string} section - 文档章节名称
     * @param {boolean} newWindow - 是否在新窗口打开，默认为true
     */
    //  const targetSection = section || heading || anchor;
    //  if (targetSection) {
    //      targetUrl += '#' + targetSection;
    //  }
    const navigateToDocument = (page, section, newWindow = true) => {
        const url = `${window.location.origin}/document?page=${encodeURIComponent(
            page
        )}&section=${encodeURIComponent(section)}`;

        if (newWindow) {
            // 在新窗口打开
            window.open(url, '_blank', 'noopener,noreferrer');
        } else {
            // 在当前窗口跳转
            router.push({
                name: 'document',
                query: {
                    page,
                    section
                }
            });
        }
    };

    /**
     * 跳转到Cline安装文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为true
     */
    const navigateToClineInstall = (newWindow = true) => {
        navigateToDocument('cline-introduction.md', 'Cline的配置与使用说明', newWindow);
    };

    /**
     * 跳转到Cline配置文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为true
     */
    const navigateToClineConfig = (newWindow = true) => {
        navigateToDocument('cline-introduction.md', 'Cline的配置与使用说明', newWindow);
    };

    /**
     * 跳转到API使用文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为true
     */
    const navigateToApiUsage = (newWindow = true) => {
        navigateToDocument('api-usage.md', '基本使用', newWindow);
    };

    /**
     * 跳转到故障排除文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为true
     */
    const navigateToTroubleshooting = (newWindow = true) => {
        navigateToDocument('troubleshooting.md', '常见问题', newWindow);
    };

    /**
     * 跳转到MCP Store插件使用文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为false
     */
    const navigateToMCPStorePlugin = (newWindow = false) => {
        navigateToDocument('mcp-store-plugin.md', 'MCP Store插件使用说明', newWindow);
    };
    /**
     * 跳转到MCP Store插件启动方式文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为false
     */
    const navigateToMCPStorePluginStart = (newWindow = false) => {
        navigateToDocument('mcp-store-plugin.md', 'start', newWindow);
    };
    /**
     * 跳转到MCP Store插件使用文档
     * @param {boolean} newWindow - 是否在新窗口打开，默认为false
     */
    const navigateToMCPStorePluginTrae = (newWindow = false) => {
        navigateToDocument('mcp-store-plugin.md', 'trae', newWindow);
    };
    /**
     */
    return {
        navigateToDocument,
        navigateToClineInstall,
        navigateToClineConfig,
        navigateToApiUsage,
        navigateToTroubleshooting,
        navigateToMCPStorePlugin,
        navigateToMCPStorePluginStart,
        navigateToMCPStorePluginTrae
    };
};
