<template>
    <div class="ide-plugins-page">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-content hero-content-left">
                <div class="view-docs-link" @click="goToDocs">
                    <span class="view-docs-link-text">v.1.0发布 : 查看使用文档</span>
                    <i class="pi pi-arrow-right text-xs"></i>
                </div>
                <h1 class="hero-title-left">
                    <span class="title-word">MCP Store</span>
                    <span class="title-word">插件</span>
                    <span class="title-word">IDE</span>
                    <span class="title-word">集成</span>
                </h1>
                <div class="solution-row">
                    <span class="solution-highlight">解决方案</span>
                </div>
                <p class="hero-description-left">
                    深度集成 MCP Store 市场与主流 AI 编程助手，为开发者提供无缝的 MCP Server
                    管理体验
                </p>
                <div class="cta-row">
                    <div class="cta-btn" @click="openVSCodeMarketplace">
                        <span>立即开始</span>
                        <i class="pi pi-arrow-right text-sm"></i>
                    </div>
                    <div class="contact-card">
                        技术支持：<span @click="handleContactClick('宁方伟')">@宁方伟</span>
                        <span @click="handleContactClick('王苗')">@王苗</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心功能区域 -->
        <section class="features-section">
            <div class="container">
                <h2 class="section-title">核心功能</h2>
                <div class="features-container">
                    <!-- 左侧导航菜单 -->
                    <div class="features-nav">
                        <div
                            class="nav-item"
                            v-for="(feature, index) in features"
                            :key="index"
                            @click="activeFeature = index"
                            :class="{ 'is-active': activeFeature === index }"
                        >
                            <div class="nav-content">
                                <span class="nav-icon"></span>
                                <h3>{{ feature.title }}</h3>
                                <p>{{ feature.description }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧图片容器 -->
                    <div class="features-visual">
                        <div class="visual-stage">
                            <!-- 图片层 -->
                            <div class="image-layer">
                                <div
                                    class="image-card"
                                    v-for="(feature, index) in featuresWithImages"
                                    :key="index"
                                    :class="{
                                        'is-active': activeFeature === index
                                    }"
                                >
                                    <div class="card-inner">
                                        <div class="image-wrapper">
                                            <img
                                                :src="feature.image"
                                                :alt="feature.title + '功能界面'"
                                                loading="lazy"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 支持的 IDE 区域 -->
        <section class="ide-support-section">
            <div class="container">
                <h2 class="section-title">支持的 IDE & 平台</h2>
                <div class="ide-grid">
                    <div class="ide-card">
                        <div class="ide-logo">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/mcp-store-plugin/vscode.png"
                                alt=""
                            />
                        </div>
                        <h3>VS Code 系列</h3>
                        <p class="ide-version">vscode(>=1.74.0)</p>
                        <div class="platform-tags">
                            <span class="tag">cline</span>
                            <span class="tag">copilot</span>
                        </div>
                    </div>
                    <div class="ide-card">
                        <div class="ide-logo">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="100px"
                                height="100px"
                                viewBox="0 0 28 28"
                                fill="none"
                            >
                                <path
                                    d="M27.4343 0H0.565657C0.253253 0 0 0.253253 0 0.565657V27.4343C0 27.7467 0.253253 28 0.565657 28H27.4343C27.7467 28 28 27.7467 28 27.4343V0.565657C28 0.253253 27.7467 0 27.4343 0Z"
                                    fill="#FF4A36"
                                ></path>
                                <path
                                    d="M22.9121 19.8093H13.8616V22.9091H22.9121V19.8093Z"
                                    fill="white"
                                ></path>
                            </svg>
                        </div>
                        <h3>Trae</h3>
                        <p class="ide-version">全版本支持</p>
                        <div class="platform-tags">
                            <span class="tag">trae</span>
                            <span class="tag">cline</span>
                        </div>
                    </div>
                    <div class="ide-card">
                        <div class="ide-logo">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/mcp-client-logo/Cursor.png"
                                alt=""
                            />
                        </div>
                        <h3>Cursor</h3>
                        <p class="ide-version">全版本支持</p>
                        <div class="platform-tags">
                            <span class="tag">cursor</span>
                            <span class="tag">cline</span>
                        </div>
                    </div>
                    <div class="ide-card">
                        <div class="ide-logo">
                            <svg fill="none" viewBox="0 0 64 64">
                                <defs>
                                    <linearGradient
                                        id="__JETBRAINS_COM__LOGO_PREFIX__1"
                                        x1="-0.391"
                                        x2="24.392"
                                        y1="7.671"
                                        y2="61.126"
                                        gradientUnits="userSpaceOnUse"
                                    >
                                        <stop offset="0.1" stop-color="#FC801D"></stop>
                                        <stop offset="0.59" stop-color="#FE2857"></stop>
                                    </linearGradient>
                                    <linearGradient
                                        id="__JETBRAINS_COM__LOGO_PREFIX__0"
                                        x1="4.325"
                                        x2="62.921"
                                        y1="59.932"
                                        y2="1.336"
                                        gradientUnits="userSpaceOnUse"
                                    >
                                        <stop offset="0.21" stop-color="#FE2857"></stop>
                                        <stop offset="0.7" stop-color="#007EFF"></stop>
                                    </linearGradient>
                                </defs>
                                <path
                                    fill="#FF8100"
                                    d="M16.45 6H4.191a4.125 4.125 0 0 0-4.124 4.19l.176 11.044a4.125 4.125 0 0 0 1.44 3.066l38.159 32.707c.747.64 1.7.993 2.684.993h11.35A4.125 4.125 0 0 0 58 53.875V42.872c0-1.19-.514-2.321-1.41-3.105L19.167 7.021A4.123 4.123 0 0 0 16.45 6Z"
                                ></path>
                                <path
                                    fill="url(#__JETBRAINS_COM__LOGO_PREFIX__1)"
                                    d="M14.988 6H4.125A4.125 4.125 0 0 0 0 10.125v12.566c0 .2.014.4.044.598l5.448 37.185A4.125 4.125 0 0 0 9.573 64h15.398a4.125 4.125 0 0 0 4.125-4.127L29.09 41.37c0-.426-.066-.849-.195-1.254l-9.98-31.245A4.126 4.126 0 0 0 14.988 6Z"
                                ></path>
                                <path
                                    fill="url(#__JETBRAINS_COM__LOGO_PREFIX__0)"
                                    d="M59.876 0H25.748a4.125 4.125 0 0 0-3.8 2.52L6.151 39.943a4.118 4.118 0 0 0-.325 1.638l.15 18.329A4.125 4.125 0 0 0 10.101 64h17.666c.806 0 1.593-.236 2.266-.678l32.11-21.109A4.123 4.123 0 0 0 64 38.766V4.125A4.125 4.125 0 0 0 59.876 0Z"
                                ></path>
                                <path fill="#000" d="M52 12H12v40h40V12Z"></path>
                                <path
                                    fill="#fff"
                                    d="M33 44H17v3h16v-3ZM17 29.383h2.98v-9.775H17v-2.616h8.843v2.616h-2.98v9.775h2.98V32H17v-2.616Zm10.643-.085h2.154a2.38 2.38 0 0 0 1.163-.279c.34-.186.602-.448.788-.788.186-.34.279-.727.279-1.163V16.992h2.926v10.28c0 .9-.207 1.709-.622 2.427a4.45 4.45 0 0 1-1.715 1.688c-.728.408-1.546.611-2.454.611h-2.519v-2.7Z"
                                ></path>
                            </svg>
                        </div>
                        <h3>JetBrains</h3>
                        <p class="ide-version">jetbrains(>=2022.3)</p>
                        <div class="platform-tags">
                            <span class="tag">lingma</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用指南区域 -->
        <section class="guide-section">
            <div class="container">
                <h2 class="section-title">使用指南</h2>
                <div class="guide-steps">
                    <div class="step-card" v-for="(step, index) in steps" :key="index">
                        <div class="step-number">{{ String(index + 1).padStart(2, '0') }}</div>
                        <h3>{{ step.title }}</h3>
                        <p>{{ step.description }}</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特殊说明区域 -->
        <section class="notes-section">
            <div class="container">
                <h2 class="section-title">重要说明</h2>
                <div class="notes-grid">
                    <div class="note-card warning">
                        <h3>⚠️ 同步延迟</h3>
                        <p>
                            Trae 默认同步 VS Code 官方插件市场，存在同步延迟。建议设置
                            <span class="link-text" @click="handleOpenVSXClick">open-vsx.org</span>
                            为插件市场地址，<span class="link-text" @click="goToMCPStorePluginTrae"
                                >详见文档</span
                            >。
                        </p>
                    </div>
                    <div class="note-card info">
                        <h3>ℹ️ 启动方式</h3>
                        <p>
                            不同 AI 编程助手的启动方式略有不同，
                            <span class="link-text" @click="goToMCPStorePluginStart">详见文档</span
                            >。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 行动号召区域 -->
        <section class="cta-section">
            <div class="container">
                <h2>准备好开始了吗？</h2>
                <p>立即体验 MCP Store 插件，提升你的开发效率</p>
                <div class="cta-actions">
                    <button class="primary-btn large" @click="openVSCodeMarketplace">
                        <span>开始使用</span>
                    </button>
                    <button class="secondary-btn large" @click="goToDocs">
                        <span>查看完整文档</span>
                    </button>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useDocumentNavigation } from '@/composables/useDocumentNavigation';

// 响应式状态
const activeFeature = ref(0); // 默认选中第一个

// 文档导航工具
const { navigateToMCPStorePlugin, navigateToMCPStorePluginStart, navigateToMCPStorePluginTrae } =
    useDocumentNavigation();

// 计算属性：过滤有图片的功能
const featuresWithImages = computed(() => {
    return features.filter(feature => feature.image);
});

// 数据定义
const features = [
    {
        icon: '🔍',
        title: '智能检索',
        description: '检索 MCP Server 列表，支持多重条件过滤',
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/mcp-store-plugin2.jpg'
    },
    {
        icon: '⚡',
        title: '快速安装',
        description: '安装 MCP Server 到指定的 AI 编程助手',
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/mcp-store-plugin2.jpg'
    },
    {
        icon: '🗑️',
        title: '便捷卸载',
        description: '从指定的 AI 编程助手卸载 MCP Server',
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/mcp-store-plugin2.jpg'
    },
    {
        icon: '📤',
        title: '一键提交',
        description: '提交 MCP Server 到 MCP Store 市场',
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/mcp-store-plugin2.jpg'
    }
];

const steps = [
    {
        title: '安装插件',
        description: '在IDE的插件市场中搜索 "mcp store" 并安装'
    },
    {
        title: '配置 API',
        description: '设置 MCP Store 市场接口地址：http://mcpstore.api.corpautohome.com/'
    },
    {
        title: '安装 Server',
        description: '在插件页面浏览并安装所需的 MCP Server'
    },
    {
        title: '启动使用',
        description: '根据不同的 AI 编程助手启动对应的 MCP Server'
    }
];

/**
 * 打开 VS Code 插件市场
 */
const openVSCodeMarketplace = () => {
    const marketplaceUrl = 'vscode:extension/autohome.mcp-store';
    window.open(marketplaceUrl);
};
/**
 * 跳转到钉钉
 */
const handleContactClick = name => {
    if (name === '宁方伟') {
        window.open(`dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=pvdhg60`, '_blank');
    } else if (name === '王苗') {
        window.open(`dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=seoyha1`, '_blank');
    }
};
/**
 * 跳转到MCP Store插件使用文档
 */
const goToDocs = () => {
    // 使用composable中的方法跳转到MCP Store插件文档，在新窗口打开
    navigateToMCPStorePlugin(true);
};
/**
 * 跳转到MCP Store插件使用文档
 */
const goToMCPStorePluginStart = () => {
    // 使用composable中的方法跳转到MCP Store插件文档，在新窗口打开
    navigateToMCPStorePluginStart(true);
};
/**
 * 跳转到MCP Store插件使用文档
 */
const goToMCPStorePluginTrae = () => {
    // 使用composable中的方法跳转到MCP Store插件文档，在新窗口打开
    navigateToMCPStorePluginTrae(true);
};
/**
 * 处理 open-vsx.org 链接点击
 */
const handleOpenVSXClick = () => {
    window.open('https://open-vsx.org/', '_blank');
};
</script>

<style lang="scss" scoped>
.ide-plugins-page {
    min-height: 100vh;
    background: #ffffff;
    color: #1a1a1a;
    overflow-x: hidden;
}

// 英雄区域
.hero-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 40px 20px 100px 140px;
    position: relative;
    overflow: hidden;
    text-align: left;
    background-color: #fff;
    background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 40px 40px;

    .hero-content {
        margin-left: 0;
        text-align: left;
    }

    .view-docs-link {
        height: 32px;
        display: flex;
        width: fit-content;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        font-size: 12px;
        color: #0033ff;
        background-color: rgba(229, 234, 255);
        cursor: pointer;
        font-weight: 400;
        padding: 0 14px;
        letter-spacing: 0.5px;

        .view-docs-link-text {
            margin-right: 30px;
        }
    }

    .hero-title-left {
        font-size: 88px;
        font-weight: 500;
        line-height: 1.2;
        margin-top: 10px;
        margin-bottom: 0.5rem;
        color: #000;
        text-align: left;

        .title-word {
            display: inline-block;
        }

        .accent-text {
            color: #2563eb;
            background: none;
            -webkit-background-clip: unset;
            -webkit-text-fill-color: unset;
            background-clip: unset;
        }
    }

    .solution-row {
        margin: 0.5rem 0 1.5rem 0;
    }
    .solution-highlight {
        display: inline-block;
        font-size: 88px;
        font-weight: 500;
        color: #0033ff;
        background: none;
        letter-spacing: 2px;
    }
    .hero-description-left {
        font-size: 18px;
        line-height: 1.7;
        margin-bottom: 2.5rem;
        color: #666;
        text-align: left;
        font-weight: 400;
    }
    .cta-row {
        display: flex;
        align-items: center;
        margin-top: 2.5rem;
        letter-spacing: 0.5px;
    }
    .cta-btn {
        background-color: #0033ff;
        width: 300px;
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        color: #fff;
        font-weight: bold;
        cursor: pointer;
        padding: 0 20px;
    }
    .contact-card {
        padding: 0 20px;
        display: flex;
        align-items: center;
        background: #fff;
        color: #0033ff;
        width: 300px;
        height: 50px;
        display: flex;
        align-items: center;
        font-size: 16px;
        span {
            cursor: pointer;
            margin-left: 10px;
        }
    }
}

// 功能区域
.features-section {
    padding: 0 140px 20px 140px;
    .section-title {
        text-align: left;
        font-size: 46px;
        font-weight: 400;
        color: #000;
        margin-top: 0;
        margin-bottom: 48px;
    }
    .features-container {
        display: flex;
        align-items: center;
    }
    .features-nav {
        flex: 0 0 480px;
        display: flex;
        flex-direction: column;
        gap: 32px;
        padding-top: 0;
        margin-top: 0;
    }
    .nav-item {
        display: flex;
        align-items: flex-start;
        gap: 24px;
        padding: 0;
        border-radius: 0;
        cursor: pointer;
        background: none;
        position: relative;
        min-height: 64px;
        box-shadow: none;
        transition: none;
        .nav-content {
            position: relative;
            display: block;
            padding-left: 22px; // 为小球留出空间
            font-weight: 400;
            h3 {
                font-size: 22px;
                margin-bottom: 6px;
                margin-top: 0;
                color: #999;
                transition: color 0.2s;
            }
            p {
                font-size: 16px;
                color: #999;
                margin: 0;
            }
            .nav-icon {
                position: absolute;
                left: 0;
                top: 8px;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                flex-shrink: 0;
            }
        }
        &.is-active {
            .nav-content {
                h3 {
                    color: #000;
                }
                p {
                    color: #666;
                }
                .nav-icon {
                    background: #0033ff;
                }
            }
        }
    }
    .features-visual {
        flex: 1;
        .visual-stage {
            width: 100%;
            height: 390px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .image-layer {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: none;
            border-radius: 24px;
        }
        .image-card {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s;
            &.is-active {
                opacity: 1;
            }
            .card-inner {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: none;
                border-radius: 24px;
                .image-wrapper {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 24px;
                    img {
                        max-width: 100%;
                        max-height: 100%;
                    }
                }
            }
        }
    }
}

// IDE 支持区域
.ide-support-section {
    padding: 80px 140px;
    background: white;

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-top: 0;
        margin-bottom: 60px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2563eb 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .ide-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;

        .ide-card {
            background: white;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            .ide-logo {
                width: 100px;
                height: 100px;
                margin: 0 auto;
                overflow: hidden;
                margin-bottom: 16px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }

            h3 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 8px;
                color: #000;
            }

            .ide-version {
                font-size: 0.9rem;
                color: #666;
                margin-bottom: 16px;
            }

            .platform-tags {
                display: flex;
                gap: 8px;
                justify-content: center;
                flex-wrap: wrap;

                .tag {
                    background: #f1f5f9;
                    color: #475569;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 0.8rem;
                    border: 1px solid #e2e8f0;
                    transition: all 0.3s ease;

                    &:hover {
                        background: #2563eb;
                        color: white;
                        border-color: #2563eb;
                    }
                }
            }
        }
    }
}

// 指南区域
.guide-section {
    padding: 0 140px;
    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-top: 0;
        margin-bottom: 60px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2563eb 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .guide-steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;

        .step-card {
            background: white;
            border-radius: 12px;
            padding: 30px 0;
            text-align: center;
            position: relative;

            .step-number {
                position: absolute;
                top: -15px;
                left: 50%;
                transform: translateX(-50%);
                background: #0033ff;
                color: white;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 500;
                font-size: 1.1rem;
                transition: all 0.3s ease;
            }

            h3 {
                font-size: 1.25rem;
                font-weight: 500;
                margin: 20px 0 16px 0;
                color: #000;
            }

            p {
                color: #666;
                line-height: 1.6;
            }
        }
    }
}

// 说明区域
.notes-section {
    padding: 80px 140px;

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        font-weight: 700;
        margin-top: 0;
        margin-bottom: 60px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2563eb 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .notes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
        gap: 36px;
        @media (max-width: 900px) {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .note-card {
            background: #fff;
            border-radius: 16px;
            padding: 36px 32px 32px 32px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            min-height: 180px;
            transition: box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s;
            cursor: pointer;

            &:hover {
                box-shadow: 0 8px 32px rgba(37, 99, 235, 0.18), 0 2px 8px rgba(0, 0, 0, 0.08);
            }

            &.warning {
                background: linear-gradient(90deg, #fff 80%, #ffeaea 100%);
            }
            &.info {
                background: linear-gradient(90deg, #fff 80%, #eaf1ff 100%);
            }

            h3 {
                font-size: 1.35rem;
                font-weight: 700;
                margin-bottom: 18px;
                color: #1a1a1a;
                display: flex;
                align-items: center;
                gap: 12px;
                z-index: 2;
                // icon 更大
                & > span,
                & > .note-icon {
                    font-size: 2rem;
                    line-height: 1;
                }
            }

            p {
                color: #444;
                line-height: 1.7;
                font-size: 1.08rem;
                z-index: 2;
                margin: 0;

                .link-text {
                    color: #0033ff;
                    cursor: pointer;
                    text-decoration: underline;
                    transition: color 0.3s ease;

                    &:hover {
                        color: #1d4ed8;
                    }
                }
            }
        }
    }
}

// CTA 区域
.cta-section {
    padding: 0 140px 80px;
    text-align: center;

    h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2563eb 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    p {
        font-size: 1.25rem;
        color: #6b7280;
        margin-bottom: 40px;
    }

    .cta-actions {
        display: flex;
        gap: 16px;
        justify-content: center;
        flex-wrap: wrap;

        .primary-btn,
        .secondary-btn {
            padding: 16px 40px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent
                );
                transition: left 0.6s ease;
            }

            &:hover::before {
                left: 100%;
            }

            &.large {
                padding: 18px 48px;
                font-size: 1.2rem;
            }
        }

        .primary-btn {
            background: #2563eb;
            color: white;
            box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);

            &:hover {
                background: #1d4ed8;
                box-shadow: 0 8px 24px rgba(37, 99, 235, 0.4);
            }
        }

        .secondary-btn {
            background: white;
            color: #2563eb;
            border: 1px solid #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

            &:hover {
                background: #f8fafc;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            }
        }
    }
}

@media (min-width: 1920px) {
    .ide-plugins-page {
        width: 1760px;
        margin: 0 auto;
    }
}
</style>
