<template>
    <div class="innovation-competition">
        <!-- 背景渐变和网格 -->
        <div class="tech-background">
            <div class="gradient-overlay"></div>
            <div class="grid-pattern"></div>
            <div class="floating-orbs">
                <div class="orb orb-1"></div>
                <div class="orb orb-2"></div>
                <div class="orb orb-3"></div>
            </div>
        </div>

        <!-- 页面头部 -->
        <div class="competition-header">
            <div class="container">
                <div class="header-content">
                    <!-- 科技感标题 -->
                    <div class="title-wrapper">
                        <div class="title-badge">
                            <span class="badge-text">AI Innovation</span>
                        </div>
                        <h1 class="competition-title">
                            <span class="title-main">
                                <span class="text-gradient">MCP</span> 创新大赛
                            </span>
                            <span class="title-sub"
                                >Model Context Protocol Innovation Competition</span
                            >
                        </h1>
                        <div class="title-line"></div>
                    </div>

                    <!-- 主描述 -->
                    <p class="competition-description">
                        释放你的创意潜能，用
                        <span class="highlight">AI 编程</span> 构建下一代智能应用
                    </p>

                    <!-- 动态统计卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card" v-for="(stat, index) in stats" :key="index">
                            <div class="stat-icon">{{ stat.icon }}</div>
                            <div class="stat-content">
                                <div class="stat-number">{{ stat.number }}</div>
                                <div class="stat-label">{{ stat.label }}</div>
                            </div>
                            <div class="stat-glow"></div>
                        </div>
                    </div>

                    <!-- CTA 按钮组 -->
                    <div class="header-actions">
                        <button class="primary-btn" @click="handleRegister">
                            <span class="btn-text">立即报名</span>
                            <div class="btn-glow"></div>
                        </button>
                        <button class="secondary-btn" @click="scrollToDetails">
                            <span class="btn-text">了解详情</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M7 10L12 15L17 10"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 比赛介绍 -->
        <section class="competition-intro">
            <div class="container">
                <div class="section-header">
                    <div class="section-badge">Competition</div>
                    <h2 class="section-title">
                        <span class="title-line">探索 AI 编程的</span>
                        <span class="title-highlight">无限可能</span>
                    </h2>
                    <p class="section-description">
                        基于 MCP 协议与前沿 AI 工具，开发创新应用，引领技术未来
                    </p>
                </div>

                <div class="intro-grid">
                    <div class="intro-card" v-for="(feature, index) in features" :key="index">
                        <div class="card-glass"></div>
                        <div class="card-content">
                            <div class="card-icon-wrapper">
                                <div class="card-icon" v-html="feature.icon"></div>
                            </div>
                            <h3 class="card-title">{{ feature.title }}</h3>
                            <p class="card-description">{{ feature.description }}</p>
                            <div class="card-stats">
                                <span class="stat">{{ feature.stat }}</span>
                            </div>
                        </div>
                        <div class="card-border"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 比赛流程 -->
        <section class="competition-process">
            <div class="container">
                <div class="section-header">
                    <div class="section-badge">Timeline</div>
                    <h2 class="section-title">
                        <span class="title-line">三个阶段</span>
                        <span class="title-highlight">成就创新之路</span>
                    </h2>
                    <p class="section-description">
                        从创意构思到原型开发，再到最终展示，层层递进展现你的实力
                    </p>
                </div>

                <div class="process-flow">
                    <div class="process-item" v-for="(stage, index) in stages" :key="index">
                        <div class="process-card">
                            <div class="card-glass"></div>
                            <div class="card-content">
                                <div class="card-header">
                                    <div class="stage-number">{{ stage.number }}</div>
                                    <div class="stage-status">{{ stage.status }}</div>
                                </div>
                                <h3 class="stage-title">{{ stage.title }}</h3>
                                <div class="stage-date">{{ stage.date }}</div>
                                <p class="stage-description">{{ stage.description }}</p>
                                <div class="stage-actions">
                                    <div
                                        class="action-item"
                                        v-for="action in stage.actions"
                                        :key="action"
                                    >
                                        {{ action }}
                                    </div>
                                </div>
                            </div>
                            <div class="card-progress">
                                <div
                                    class="progress-bar"
                                    :style="{ width: stage.progress + '%' }"
                                ></div>
                            </div>
                            <div class="card-border"></div>
                        </div>

                        <!-- 连接线 -->
                        <div class="process-connector" v-if="index < stages.length - 1">
                            <div class="connector-line"></div>
                            <div class="connector-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 奖项设置 -->
        <section class="competition-prizes">
            <div class="container">
                <div class="section-header">
                    <div class="section-badge">Rewards</div>
                    <h2 class="section-title">
                        <span class="title-line">丰厚奖励</span>
                        <span class="title-highlight">激发无限创意</span>
                    </h2>
                    <p class="section-description">
                        总奖金池高达 ¥100,000+，更有流量扶持与投资机会
                    </p>
                </div>

                <div class="prizes-showcase">
                    <div
                        class="prize-card"
                        v-for="(prize, index) in prizes"
                        :key="index"
                        :class="prize.type"
                    >
                        <div class="card-glass"></div>
                        <div class="prize-content">
                            <div class="prize-header">
                                <div class="prize-rank">{{ prize.rank }}</div>
                                <div class="prize-badge">{{ prize.badge }}</div>
                            </div>
                            <h3 class="prize-title">{{ prize.title }}</h3>
                            <div class="prize-amount">{{ prize.amount }}</div>
                            <div class="prize-benefits">
                                <div
                                    class="benefit-item"
                                    v-for="benefit in prize.benefits"
                                    :key="benefit"
                                >
                                    <span class="benefit-icon">✦</span>
                                    <span class="benefit-text">{{ benefit }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-border"></div>
                    </div>
                </div>

                <!-- 评估维度 -->
                <div class="evaluation-metrics">
                    <h3 class="metrics-title">评选维度</h3>
                    <div class="metrics-grid">
                        <div class="metric-item" v-for="metric in metrics" :key="metric.name">
                            <div class="metric-circle">
                                <div
                                    class="metric-progress"
                                    :style="{ '--progress': metric.weight + '%' }"
                                >
                                    <span class="metric-percentage">{{ metric.weight }}%</span>
                                </div>
                            </div>
                            <div class="metric-info">
                                <h4 class="metric-name">{{ metric.name }}</h4>
                                <p class="metric-description">{{ metric.description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 报名按钮 -->
        <section class="competition-cta">
            <div class="container">
                <div class="cta-content">
                    <div class="cta-glass"></div>
                    <h2>立即报名参加</h2>
                    <p>不要错过这个展示才华的机会</p>
                    <button class="cta-button" @click="handleRegister">立即报名</button>
                </div>
            </div>
        </section>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 数据定义
const stats = ref([
    { icon: '💰', number: '¥100,000+', label: '总奖金池' },
    { icon: '🏆', number: '5', label: '奖项等级' },
    { icon: '⏰', number: '90', label: '天竞赛期' },
    { icon: '🌟', number: '∞', label: '创意无限' }
]);

const features = ref([
    {
        icon: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2L2 7V10C2 16 6 20.6 12 22C18 20.6 22 16 22 10V7L12 2Z"/></svg>',
        title: '前沿技术',
        description: '基于 MCP 协议与 AI 工具的深度融合，探索编程新范式',
        stat: 'AI 驱动'
    },
    {
        icon: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z"/></svg>',
        title: '创新赛道',
        description: '从效率工具到创意互动，多元化主题释放无限可能',
        stat: '多维度'
    },
    {
        icon: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/></svg>',
        title: '生态共建',
        description: '汇聚全球开发者智慧，共同构建 AI 编程生态系统',
        stat: '全球协作'
    }
]);

const stages = ref([
    {
        number: '01',
        title: '报名开发',
        date: '即日起 - 6月30日',
        status: '进行中',
        description: '使用 Trae AI 工具和掘金 MCP 功能实现创意作品',
        actions: ['配置开发环境', '创作原型作品', '一键部署上线'],
        progress: 75
    },
    {
        number: '02',
        title: '作品提交',
        date: '截止6月30日',
        status: '即将开始',
        description: '提交作品链接、展示创意成果，参与社区互动',
        actions: ['提交作品信息', '社交媒体分享', '社区互动推广'],
        progress: 30
    },
    {
        number: '03',
        title: '评审颁奖',
        date: '7月1日-7月30日',
        status: '待启动',
        description: '专家评审与社区投票，决出最终获奖者',
        actions: ['专家评审', '社区投票', '线上颁奖'],
        progress: 0
    }
]);

const prizes = ref([
    {
        type: 'champion',
        rank: '🏆',
        badge: 'VibeMaker',
        title: '冠军奖',
        amount: '¥5,000',
        benefits: ['10万+曝光流量', '投资机构对接', '技术专家指导', '官方认证证书']
    },
    {
        type: 'first',
        rank: '🥇',
        badge: '一等奖',
        title: '创新先锋',
        amount: '¥2,000',
        benefits: ['5万+曝光流量', '技术社区推荐', '行业大会分享', '精美奖杯证书']
    },
    {
        type: 'second',
        rank: '🥈',
        badge: '二等奖',
        title: '技术新星',
        amount: '¥1,000',
        benefits: ['3万+曝光流量', '作品专栏推广', '社区认证标识']
    },
    {
        type: 'third',
        rank: '🥉',
        badge: '三等奖',
        title: '创意达人',
        amount: '¥500',
        benefits: ['1万+曝光流量', '作品收录展示', '参与证书']
    }
]);

const metrics = ref([
    { name: '功能性', description: '作品完整度与操作流畅性', weight: 40 },
    { name: '创新性', description: '设计新颖性与实用价值', weight: 30 },
    { name: '商业性', description: '市场潜力与发展前景', weight: 20 },
    { name: '体验性', description: 'UI设计与交互友好度', weight: 10 }
]);

// 方法定义
const handleRegister = () => {
    window.open('https://aicoding.juejin.cn/vibecoding', '_blank');
};

const scrollToDetails = () => {
    const element = document.querySelector('.competition-intro');
    element?.scrollIntoView({ behavior: 'smooth' });
};

// 生命周期
onMounted(() => {
    // 页面加载完成后的初始化逻辑
    console.log('Innovation Competition page loaded');
});
</script>

<style lang="scss" scoped>
// 全局变量
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gold-gradient: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 16px 64px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 32px 128px rgba(0, 0, 0, 0.2);
}

.innovation-competition {
    min-height: 100vh;
    background: #0a0a0a;
    position: relative;
    overflow-x: hidden;
    color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

// 背景渐变和网格
.tech-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;

    .gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    }

    .grid-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
        background-size: 50px 50px;
        opacity: 0.3;
    }

    .floating-orbs {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;

        .orb {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: floatOrb 20s infinite ease-in-out;

            &.orb-1 {
                width: 200px;
                height: 200px;
                top: 10%;
                left: 10%;
                animation-delay: -5s;
            }

            &.orb-2 {
                width: 150px;
                height: 150px;
                top: 60%;
                right: 15%;
                animation-delay: -10s;
            }

            &.orb-3 {
                width: 100px;
                height: 100px;
                bottom: 20%;
                left: 20%;
                animation-delay: -15s;
            }
        }
    }
}

@keyframes floatOrb {
    0%,
    100% {
        transform: translateY(0) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-30px) scale(1.1);
        opacity: 0.5;
    }
    50% {
        transform: translateY(0) scale(1);
        opacity: 0.3;
    }
    75% {
        transform: translateY(30px) scale(1.1);
        opacity: 0.5;
    }
}

// 页面头部
.competition-header {
    padding: 120px 0 80px;
    text-align: center;
    color: white;
    position: relative;
    z-index: 3;

    .header-content {
        max-width: 900px;
        margin: 0 auto;
    }

    // 标题装饰
    .title-wrapper {
        margin-bottom: 40px;

        .title-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 20px;
            border-radius: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);

            .badge-text {
                font-size: 0.9rem;
                font-weight: 600;
                color: white;
                text-transform: uppercase;
                letter-spacing: 1px;
                background: linear-gradient(45deg, #fff, #f0f0f0, #ffd700);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        }

        .competition-title {
            margin-bottom: 30px;

            .title-main {
                display: block;
                font-size: clamp(3rem, 8vw, 5rem);
                font-weight: 800;
                margin-bottom: 15px;
                background: linear-gradient(45deg, #fff, #f0f0f0, #ffd700);
                background-size: 200% 200%;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                animation: gradientShift 3s ease-in-out infinite;

                .text-gradient {
                    background: linear-gradient(45deg, #ffd700, #ffed4e);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
                }
            }

            .title-sub {
                display: block;
                font-size: 1.3rem;
                opacity: 0.8;
                font-weight: 300;
                letter-spacing: 2px;
                text-transform: uppercase;
            }
        }

        .title-line {
            width: 80px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ffd700, transparent);
            margin: 30px auto 0;
            animation: pulse 2s ease-in-out infinite;
        }
    }

    .competition-description {
        font-size: 1.4rem;
        margin-bottom: 60px;
        opacity: 0.9;
        line-height: 1.8;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;

        .highlight {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }
    }

    // 统计卡片网格
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 30px;
        margin: 60px 0;

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                transform: translateY(-10px) scale(1.05);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                border-color: rgba(255, 215, 0, 0.5);
                background: rgba(255, 255, 255, 0.1);

                .stat-glow {
                    opacity: 1;
                }
            }

            .stat-icon {
                font-size: 2.5rem;
                margin-bottom: 15px;
                filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
            }

            .stat-content {
                .stat-number {
                    font-size: 2.2rem;
                    font-weight: 800;
                    margin-bottom: 8px;
                    background: linear-gradient(45deg, #ffd700, #ffed4e);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                .stat-label {
                    font-size: 0.9rem;
                    opacity: 0.8;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
            }

            .stat-glow {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(circle at center, rgba(255, 215, 0, 0.1), transparent);
                opacity: 0;
                transition: opacity 0.4s ease;
            }
        }
    }

    // 按钮组
    .header-actions {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-top: 50px;
        flex-wrap: wrap;

        .primary-btn {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            border: none;
            padding: 18px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(255, 215, 0, 0.3);

            &:hover {
                transform: translateY(-3px);
                box-shadow: 0 16px 64px rgba(255, 215, 0, 0.4);

                .btn-glow {
                    transform: translateX(100%);
                }
            }

            .btn-text {
                position: relative;
                z-index: 2;
            }

            .btn-glow {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.3),
                    transparent
                );
                transition: transform 0.6s ease;
            }
        }

        .secondary-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px 30px;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(10px);

            &:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }

            .btn-icon {
                width: 20px;
                height: 20px;
                transition: transform 0.3s ease;
            }

            &:hover .btn-icon {
                transform: translateY(3px);
            }
        }
    }
}

@keyframes gradientShift {
    0%,
    100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 0.5;
        transform: scaleX(1);
    }
    50% {
        opacity: 1;
        transform: scaleX(1.2);
    }
}

// 通用section样式
section {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.02);
    position: relative;
    backdrop-filter: blur(10px);

    .section-header {
        text-align: center;
        margin-bottom: 80px;

        .section-badge {
            display: inline-block;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            color: #ffffff;
            margin-bottom: 20px;
            font-weight: 700;

            .title-line {
                display: block;
                margin-bottom: 10px;
            }

            .title-highlight {
                background: linear-gradient(45deg, #ffd700, #ffed4e);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        }

        .section-description {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.7;
        }
    }
}

// 比赛介绍
.competition-intro {
    .intro-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
        margin-top: 60px;

        .intro-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 24px;
            padding: 40px 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);

            &:hover {
                transform: translateY(-15px);
                box-shadow: 0 32px 128px rgba(0, 0, 0, 0.3);
                background: rgba(255, 255, 255, 0.1);

                .card-glass {
                    opacity: 1;
                }

                .card-border {
                    opacity: 1;
                }
            }

            .card-glass {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 24px;
                z-index: 1;
                opacity: 0;
                transition: opacity 0.4s ease;
            }

            .card-content {
                position: relative;
                z-index: 2;
                text-align: center;

                .card-icon-wrapper {
                    width: 80px;
                    height: 80px;
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 25px;
                    position: relative;

                    .card-icon {
                        width: 40px;
                        height: 40px;
                        color: white;
                        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
                    }
                }

                .card-title {
                    font-size: 1.5rem;
                    color: #ffffff;
                    margin-bottom: 15px;
                    font-weight: 600;
                }

                .card-description {
                    color: rgba(255, 255, 255, 0.8);
                    line-height: 1.7;
                    margin-bottom: 20px;
                }

                .card-stats {
                    .stat {
                        display: inline-block;
                        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                        color: white;
                        padding: 6px 16px;
                        border-radius: 20px;
                        font-size: 0.8rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }
                }
            }

            .card-border {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, transparent, #ffd700, transparent);
                opacity: 0;
                transition: opacity 0.4s ease;
            }
        }
    }
}

// 比赛流程
.competition-process {
    background: rgba(255, 255, 255, 0.02);

    .process-flow {
        display: flex;
        flex-direction: column;
        gap: 40px;
        max-width: 900px;
        margin: 0 auto;

        .process-item {
            display: flex;
            align-items: center;
            gap: 30px;
            position: relative;

            .process-card {
                flex: 1;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                position: relative;
                overflow: hidden;
                transition: all 0.3s ease;
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.1);

                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
                    background: rgba(255, 255, 255, 0.1);

                    .card-glass {
                        opacity: 1;
                    }
                }

                .card-glass {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.05);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 20px;
                    z-index: 1;
                    opacity: 0;
                    transition: opacity 0.4s ease;
                }

                .card-content {
                    position: relative;
                    z-index: 2;

                    .card-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 20px;

                        .stage-number {
                            width: 50px;
                            height: 50px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 1.2rem;
                            font-weight: 700;
                        }

                        .stage-status {
                            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                            color: white;
                            padding: 4px 12px;
                            border-radius: 12px;
                            font-size: 0.8rem;
                            font-weight: 600;
                        }
                    }

                    .stage-title {
                        font-size: 1.4rem;
                        color: #ffffff;
                        margin-bottom: 10px;
                        font-weight: 600;
                    }

                    .stage-date {
                        color: #4facfe;
                        font-weight: 600;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                    }

                    .stage-description {
                        color: rgba(255, 255, 255, 0.8);
                        line-height: 1.6;
                        margin-bottom: 20px;
                    }

                    .stage-actions {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;

                        .action-item {
                            background: rgba(255, 255, 255, 0.1);
                            color: rgba(255, 255, 255, 0.8);
                            padding: 6px 12px;
                            border-radius: 12px;
                            font-size: 0.8rem;
                            border: 1px solid rgba(255, 255, 255, 0.2);
                        }
                    }
                }

                .card-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: rgba(255, 255, 255, 0.1);

                    .progress-bar {
                        height: 100%;
                        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                        transition: width 1s ease;
                    }
                }

                .card-border {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: linear-gradient(90deg, transparent, #ffd700, transparent);
                    opacity: 0.5;
                }
            }

            .process-connector {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 10px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                z-index: 1;

                .connector-line {
                    width: 2px;
                    height: 60px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .connector-dot {
                    width: 8px;
                    height: 8px;
                    background: #ffd700;
                    border-radius: 50%;
                    animation: glow 2s ease-in-out infinite alternate;
                }
            }
        }
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px #ffd700;
    }
    100% {
        box-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700;
    }
}

// 奖项设置
.competition-prizes {
    .prizes-showcase {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        margin-bottom: 80px;

        .prize-card {
            position: relative;
            border-radius: 24px;
            padding: 40px 30px;
            text-align: center;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);

            &:hover {
                transform: translateY(-15px) scale(1.05);
                box-shadow: 0 32px 128px rgba(0, 0, 0, 0.3);

                .card-glass {
                    opacity: 1;
                }
            }

            &.champion {
                background: linear-gradient(
                    135deg,
                    rgba(255, 215, 0, 0.2) 0%,
                    rgba(255, 237, 78, 0.2) 100%
                );
                color: #ffffff;
            }

            &.first {
                background: linear-gradient(
                    135deg,
                    rgba(192, 192, 192, 0.2) 0%,
                    rgba(229, 229, 229, 0.2) 100%
                );
                color: #ffffff;
            }

            &.second {
                background: linear-gradient(
                    135deg,
                    rgba(205, 127, 50, 0.2) 0%,
                    rgba(218, 165, 32, 0.2) 100%
                );
                color: #ffffff;
            }

            &.third {
                background: linear-gradient(
                    135deg,
                    rgba(184, 134, 11, 0.2) 0%,
                    rgba(218, 165, 32, 0.2) 100%
                );
                color: #ffffff;
            }

            .card-glass {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 24px;
                z-index: 1;
                opacity: 0;
                transition: opacity 0.4s ease;
            }

            .prize-content {
                position: relative;
                z-index: 2;

                .prize-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 20px;

                    .prize-rank {
                        font-size: 3rem;
                        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
                    }

                    .prize-badge {
                        background: rgba(255, 255, 255, 0.2);
                        color: inherit;
                        padding: 4px 12px;
                        border-radius: 12px;
                        font-size: 0.8rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }
                }

                .prize-title {
                    font-size: 1.5rem;
                    margin-bottom: 15px;
                    font-weight: 700;
                }

                .prize-amount {
                    font-size: 2.5rem;
                    font-weight: 800;
                    margin-bottom: 25px;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    background: linear-gradient(45deg, #ffd700, #ffed4e);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                .prize-benefits {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;

                    .benefit-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        font-size: 0.9rem;

                        .benefit-icon {
                            color: #ffd700;
                            font-weight: bold;
                        }

                        .benefit-text {
                            opacity: 0.9;
                        }
                    }
                }
            }

            .card-border {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, transparent, #ffd700, transparent);
                opacity: 0.5;
            }
        }
    }

    // 评估维度
    .evaluation-metrics {
        .metrics-title {
            text-align: center;
            font-size: 2rem;
            color: #ffffff;
            margin-bottom: 50px;
            font-weight: 600;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;

            .metric-item {
                text-align: center;

                .metric-circle {
                    width: 120px;
                    height: 120px;
                    margin: 0 auto 20px;
                    position: relative;

                    .metric-progress {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background: conic-gradient(
                            from 0deg,
                            linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) 0deg,
                            linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)
                                calc(var(--progress) * 3.6deg),
                            rgba(255, 255, 255, 0.1) calc(var(--progress) * 3.6deg),
                            rgba(255, 255, 255, 0.1) 360deg
                        );
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        position: relative;

                        &::before {
                            content: '';
                            width: 80px;
                            height: 80px;
                            background: rgba(255, 255, 255, 0.05);
                            border-radius: 50%;
                            position: absolute;
                            backdrop-filter: blur(10px);
                        }

                        .metric-percentage {
                            position: relative;
                            z-index: 2;
                            font-size: 1.2rem;
                            font-weight: 700;
                            color: #ffffff;
                        }
                    }
                }

                .metric-info {
                    .metric-name {
                        font-size: 1.2rem;
                        color: #ffffff;
                        margin-bottom: 10px;
                        font-weight: 600;
                    }

                    .metric-description {
                        color: rgba(255, 255, 255, 0.8);
                        line-height: 1.6;
                        font-size: 0.9rem;
                    }
                }
            }
        }
    }
}

// 报名按钮
.competition-cta {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.02);

    .cta-content {
        max-width: 900px;
        margin: 0 auto;
        text-align: center;
        position: relative;
        z-index: 2;
        padding: 60px 40px;
        border-radius: 24px;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);

        .cta-glass {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            z-index: 1;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        &:hover .cta-glass {
            opacity: 1;
        }

        h2 {
            font-size: 2.5rem;
            color: #ffffff;
            margin-bottom: 20px;
            font-weight: 700;
        }

        p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
            line-height: 1.8;
        }

        .cta-button {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            border: none;
            padding: 18px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(255, 215, 0, 0.3);

            &:hover {
                transform: translateY(-3px);
                box-shadow: 0 16px 64px rgba(255, 215, 0, 0.4);

                .btn-shine {
                    transform: translateX(100%);
                }
            }

            .btn-text {
                position: relative;
                z-index: 2;
            }

            .btn-shine {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.3),
                    transparent
                );
                transition: transform 0.6s ease;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .competition-header {
        padding: 80px 0 60px;

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .header-actions {
            flex-direction: column;
            align-items: center;

            .primary-btn,
            .secondary-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    }

    section {
        padding: 60px 0;
    }

    .competition-process .process-flow {
        .process-item {
            flex-direction: column;
            text-align: center;

            .process-connector {
                transform: rotate(90deg);
                margin: 20px 0;
            }
        }
    }

    .prizes-showcase {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }
}
</style>
