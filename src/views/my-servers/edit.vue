<template>
    <ServerLayout>
        <!-- 顶部标题区域 -->
        <template #header>
            <div class="header-container">
                <!-- 左侧返回按钮 -->
                <div class="header-left">
                    <Button
                        icon="pi pi-arrow-left"
                        class="back-btn p-button-rounded p-button-text"
                        aria-label="返回"
                        @click="navigateBack"
                    />
                </div>

                <!-- 中间标题区域 -->
                <div class="header-center">
                    <div class="title-wrapper">
                        <h2 class="content-title">
                            <span class="title-prefix">{{
                                isEdit ? '当前正在编辑：' : '完善创建信息'
                            }}</span>
                            <span
                                v-if="isEdit && myServersStore.serverDetail.name"
                                class="server-name"
                                >{{ myServersStore.serverDetail.name }}</span
                            >
                            <p class="content-subtitle">
                                请完善信息以便于server的{{ isEdit ? '编辑' : '创建' }}
                            </p>
                        </h2>
                    </div>
                </div>

                <!-- 右侧元数据区域 -->
                <div class="header-right">
                    <div v-if="isEdit && myServersStore.serverDetail" class="server-metadata">
                        <span class="metadata-item">
                            <i class="pi pi-calendar"></i> 发布时间:
                            {{ formatDateTime(myServersStore.serverDetail.publishTime) }}
                        </span>
                        <span class="metadata-item">
                            <i class="pi pi-clock"></i> 更新时间:
                            {{ formatDateTime(myServersStore.serverDetail.modifiedStime) }}
                        </span>
                    </div>
                </div>
            </div>
        </template>

        <div class="page-content">
            <!-- 锚点导航移到上方 -->
            <div class="anchor-navigation">
                <div class="anchor-list">
                    <div
                        class="anchor-item active"
                        @click="scrollToSection('required-section', $event)"
                    >
                        <div class="anchor-icon bg-red-500">
                            <i class="pi pi-check-circle text-sm"></i>
                        </div>
                        <div class="anchor-label text-red-500 text-sm ml-2 font-medium">
                            必填信息
                        </div>
                    </div>
                    <div class="anchor-item" @click="scrollToSection('optional-section', $event)">
                        <div class="anchor-icon bg-green-500">
                            <i class="pi pi-plus-circle text-sm"></i>
                        </div>
                        <div class="anchor-label text-green-500 text-sm ml-2 font-medium">
                            选填信息
                        </div>
                    </div>
                </div>
            </div>

            <form class="p-fluid" @submit.prevent="submit">
                <!-- 动态渲染表单字段 -->
                <div class="form-sections">
                    <!-- 必填信息部分 -->
                    <div id="required-section" class="form-section">
                        <div class="form-section-header">
                            <div class="header-icon text-white-500 bg-red-500">
                                <i class="pi pi-check-circle"></i>
                            </div>
                            <div class="header-text">
                                <h4 class="section-title text-red-500">必填信息</h4>
                                <p class="section-desc text-red-500 font-medium">
                                    创建MCP Server所需的必填信息
                                </p>
                            </div>
                        </div>

                        <div class="form-section-content">
                            <template
                                v-for="(field, index) in getFieldsByRequired(true)"
                                :key="field.name"
                            >
                                <div :class="['form-field', `type-${field.type}`]">
                                    <div class="form-field-column">
                                        <label
                                            :for="field.name"
                                            class="form-label"
                                            :data-index="index + 1"
                                        >
                                            {{ field.label }}
                                            <!-- 字段提示说明 -->
                                            <span v-if="field.hint" class="field-hint">{{
                                                field.hint
                                            }}</span>
                                            <Button
                                                v-if="field.showExample"
                                                type="button"
                                                icon="pi pi-thumbs-up"
                                                label="参考示例"
                                                class="example-btn p-button-sm text-xs"
                                                @click="openExampleDialog(field)"
                                            />
                                        </label>

                                        <div
                                            class="form-field-input flex flex-wrap align-items-center gap-2"
                                        >
                                            <!-- 文本输入框 -->
                                            <template v-if="field.type === 'text'">
                                                <InputText
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    class="w-full text-sm"
                                                    :class="{ 'p-invalid': errors[field.name] }"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请输入${field.label}`
                                                    "
                                                    autocomplete="off"
                                                />
                                            </template>

                                            <!-- 文本域 -->
                                            <template v-else-if="field.type === 'textarea'">
                                                <Textarea
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    class="w-full text-sm"
                                                    :class="{
                                                        'p-invalid': errors[field.name],
                                                        'code-textarea': field.isCode
                                                    }"
                                                    :rows="field.rows || 3"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请输入${field.label}`
                                                    "
                                                />
                                            </template>

                                            <!-- 单选按钮组 -->
                                            <template v-else-if="field.type === 'radio'">
                                                <div class="language-options text-sm mt-3">
                                                    <div
                                                        v-for="option in field.options"
                                                        :key="option.value"
                                                        class="language-option"
                                                    >
                                                        <RadioButton
                                                            v-model="formData[field.name]"
                                                            :input-id="`${field.name}-${option.value}`"
                                                            :name="field.name"
                                                            :value="option.value"
                                                            :disabled="field.disabled"
                                                        />
                                                        <label
                                                            :for="`${field.name}-${option.value}`"
                                                            class="ml-2 text-sm"
                                                            >{{ option.label }}</label
                                                        >
                                                    </div>
                                                </div>
                                            </template>

                                            <!-- 下拉选择框 -->
                                            <template v-else-if="field.type === 'dropdown'">
                                                <Dropdown
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    :options="field.options"
                                                    option-label="label"
                                                    option-value="value"
                                                    class="w-full text-sm"
                                                    :class="{ 'p-invalid': errors[field.name] }"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请选择${field.label}`
                                                    "
                                                />
                                            </template>

                                            <!-- 多选框 -->
                                            <template v-else-if="field.type === 'multiselect'">
                                                <MultiSelect
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    :options="field.options"
                                                    option-label="label"
                                                    option-value="value"
                                                    display="chip"
                                                    class="w-full text-sm"
                                                    :class="{ 'p-invalid': errors[field.name] }"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请选择${field.label}`
                                                    "
                                                    :pt="{
                                                        item: {
                                                            style: {
                                                                fontSize: '14px'
                                                            }
                                                        }
                                                    }"
                                                />
                                            </template>

                                            <!-- Markdown编辑器 -->
                                            <template v-else-if="field.type === 'markdown'">
                                                <MarkdownEditor
                                                    v-model="formData[field.name]"
                                                    height="500px"
                                                    :disabled="field.disabled"
                                                />
                                            </template>

                                            <!-- 验证错误提示 -->
                                            <InlineMessage
                                                v-if="errors[field.name]"
                                                severity="error"
                                                class="text-sm"
                                            >
                                                {{ errors[field.name] }}
                                            </InlineMessage>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 选填信息部分 -->
                    <div id="optional-section" class="form-section">
                        <div class="form-section-header">
                            <div class="header-icon text-white-500 bg-green-500">
                                <i class="pi pi-plus-circle"></i>
                            </div>
                            <div class="header-text">
                                <h4 class="section-title text-green-500">选填信息</h4>
                                <p class="section-desc text-green-500 font-medium">
                                    MCP Server的补充信息（可选填）
                                </p>
                            </div>
                        </div>

                        <div class="form-section-content">
                            <!-- LOGO 预览和输入框独立渲染，始终显示在选填信息顶部 -->
                            <div class="form-field type-logoUrl">
                                <div class="form-field-column">
                                    <label for="logoUrl" class="form-label" data-index="1"
                                        >Logo 地址
                                        <span class="field-hint"
                                            >填写服务Logo图片的URL地址，支持JPG、PNG等格式，建议使用https协议。选择"汽车之家"来源时将自动填充默认Logo。</span
                                        ></label
                                    >
                                    <div class="logo-preview-container">
                                        <div class="logo-preview-area">
                                            <div v-if="!formData.logoUrl" class="logo-placeholder">
                                                <i class="pi pi-image text-gray-400"></i>
                                                <span class="placeholder-text">暂无Logo</span>
                                            </div>
                                            <div v-else class="logo-display">
                                                <img
                                                    :src="formData.logoUrl"
                                                    alt="Logo预览"
                                                    class="logo-preview-image"
                                                    @error="handleLogoError"
                                                    @load="handleLogoLoad"
                                                />
                                                <div v-if="logoLoading" class="logo-loading">
                                                    <i class="pi pi-spin pi-spinner"></i>
                                                    <span>加载中...</span>
                                                </div>
                                            </div>
                                        </div>
                                        <InputText
                                            id="logoUrl"
                                            v-model="formData.logoUrl"
                                            class="w-full text-sm"
                                            :class="{ 'p-invalid': errors.logoUrl }"
                                            :placeholder="'请输入Logo图片的URL地址'"
                                            @input="handleLogoUrlChange"
                                        />
                                        <div v-if="logoError" class="logo-error">
                                            <i class="pi pi-exclamation-triangle text-red-500"></i>
                                            <span class="error-text"
                                                >图片加载失败，请检查URL是否正确</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 其余选填字段 -->
                            <template
                                v-for="(field, index) in getFieldsByRequired(false)"
                                :key="field.name"
                            >
                                <div
                                    v-if="field.name !== 'logoUrl'"
                                    :class="['form-field', `type-${field.type}`]"
                                >
                                    <div class="form-field-column">
                                        <label
                                            :for="field.name"
                                            class="form-label"
                                            :data-index="index + 2"
                                        >
                                            {{ field.label
                                            }}<span v-if="field.required" class="required-mark"
                                                >*</span
                                            >
                                            <span v-if="field.hint" class="field-hint">{{
                                                field.hint
                                            }}</span>
                                            <Button
                                                v-if="field.showExample"
                                                type="button"
                                                icon="pi pi-thumbs-up"
                                                label="参考示例"
                                                class="example-btn p-button-sm text-xs"
                                                @click="openExampleDialog(field)"
                                            />
                                        </label>
                                        <div
                                            class="form-field-input flex flex-wrap align-items-center gap-2"
                                        >
                                            <template v-if="field.type === 'text'">
                                                <InputText
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    class="w-full text-sm"
                                                    :class="{ 'p-invalid': errors[field.name] }"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请输入${field.label}`
                                                    "
                                                    autocomplete="off"
                                                />
                                            </template>
                                            <template v-else-if="field.type === 'textarea'">
                                                <Textarea
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    class="w-full text-sm"
                                                    :class="{
                                                        'p-invalid': errors[field.name],
                                                        'code-textarea': field.isCode
                                                    }"
                                                    :rows="field.rows || 3"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请输入${field.label}`
                                                    "
                                                />
                                            </template>
                                            <template v-else-if="field.type === 'radio'">
                                                <div class="language-options text-sm mt-2">
                                                    <div
                                                        v-for="option in field.options"
                                                        :key="option.value"
                                                        class="language-option"
                                                    >
                                                        <RadioButton
                                                            v-model="formData[field.name]"
                                                            :input-id="`${field.name}-${option.value}`"
                                                            :name="field.name"
                                                            :value="option.value"
                                                            :disabled="field.disabled"
                                                        />
                                                        <label
                                                            :for="`${field.name}-${option.value}`"
                                                            class="ml-2 text-sm"
                                                            >{{ option.label }}</label
                                                        >
                                                    </div>
                                                </div>
                                            </template>
                                            <template v-else-if="field.type === 'dropdown'">
                                                <Dropdown
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    :options="field.options"
                                                    option-label="label"
                                                    option-value="value"
                                                    class="w-full text-sm"
                                                    :class="{ 'p-invalid': errors[field.name] }"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请选择${field.label}`
                                                    "
                                                />
                                            </template>
                                            <template v-else-if="field.type === 'multiselect'">
                                                <MultiSelect
                                                    :id="field.name"
                                                    v-model="formData[field.name]"
                                                    :options="field.options"
                                                    option-label="label"
                                                    option-value="value"
                                                    display="chip"
                                                    class="w-full text-sm"
                                                    :class="{ 'p-invalid': errors[field.name] }"
                                                    :disabled="field.disabled"
                                                    :placeholder="
                                                        field.placeholder || `请选择${field.label}`
                                                    "
                                                />
                                            </template>
                                            <template v-else-if="field.type === 'markdown'">
                                                <MarkdownEditor
                                                    v-model="formData[field.name]"
                                                    height="500px"
                                                    :disabled="field.disabled"
                                                />
                                            </template>
                                            <InlineMessage
                                                v-if="errors[field.name]"
                                                severity="error"
                                                class="text-sm"
                                            >
                                                {{ errors[field.name] }}
                                            </InlineMessage>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 按钮组 -->
                <div class="form-actions">
                    <div class="action-container">
                        <Button
                            label="取消"
                            class="p-button-outlined text-sm"
                            severity="danger"
                            :disabled="loading"
                            icon="pi pi-times"
                            rounded
                            @click="navigateToMyServers"
                        />
                        <Button
                            v-if="isEdit"
                            icon="pi pi-refresh"
                            label="恢复原始数据"
                            class="p-button-outlined p-button-secondary text-sm"
                            :disabled="loading"
                            rounded
                            @click="resetToOriginal"
                        />
                        <Loading v-if="loading" text="提交中..." />
                        <Button
                            v-else
                            :label="isEdit ? '保存更改' : '创建'"
                            type="submit"
                            :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                            class="text-sm"
                            rounded
                            :pt="{
                                root: {
                                    style: {
                                        backgroundColor: '#005aff',
                                        color: '#fff'
                                    }
                                }
                            }"
                        />
                    </div>
                </div>
            </form>
        </div>

        <!-- 示例弹窗 -->
        <Dialog
            v-model:visible="exampleDialogVisible"
            :header="currentExample.title"
            :style="{ width: '40vw' }"
            modal
        >
            <div class="example-dialog-content">
                <p class="example-description text-sm">{{ currentExample.description }}</p>

                <div
                    v-if="currentExample.platforms && currentExample.platforms.length > 0"
                    class="example-selector-container"
                >
                    <div class="selectors-row">
                        <!-- 协议类型选择器 -->
                        <div class="protocol-selector">
                            <span class="selector-label text-sm">协议类型：</span>
                            <div class="protocol-buttons">
                                <Button
                                    v-for="(typeGroup, typeIndex) in currentExample.platforms"
                                    :key="typeIndex"
                                    :label="typeGroup.name"
                                    :class="[
                                        'protocol-btn text-xs',
                                        {
                                            'p-button-outlined': selectedProtocolIndex !== typeIndex
                                        }
                                    ]"
                                    :pt="{
                                        root: {
                                            style: {
                                                backgroundColor:
                                                    selectedProtocolIndex === typeIndex
                                                        ? '#005aff'
                                                        : 'transparent'
                                            }
                                        }
                                    }"
                                    size="small"
                                    rounded
                                    @click="selectProtocol(typeIndex)"
                                />
                            </div>
                        </div>

                        <!-- 平台选择器 -->
                        <div class="platform-selector" v-if="false">
                            <span class="selector-label text-sm">平台：</span>
                            <div class="platform-buttons">
                                <Button
                                    v-for="(platform, platformIndex) in currentExample.platforms[
                                        selectedProtocolIndex
                                    ].platforms"
                                    :key="platformIndex"
                                    :label="platform.name"
                                    :class="[
                                        'platform-btn text-xs',
                                        {
                                            'p-button-outlined':
                                                selectedPlatformIndex !== platformIndex
                                        }
                                    ]"
                                    :pt="{
                                        root: {
                                            style: {
                                                backgroundColor:
                                                    selectedPlatformIndex === platformIndex
                                                        ? '#005aff'
                                                        : 'transparent'
                                            }
                                        }
                                    }"
                                    rounded
                                    size="small"
                                    @click="selectPlatform(platformIndex)"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- 代码展示区域 -->
                    <div class="code-display-area">
                        <Textarea
                            v-model="selectedPlatformContent"
                            class="w-full code-textarea text-sm"
                            :rows="15"
                        />
                    </div>
                </div>
                <div v-else>
                    <Textarea
                        v-if="currentExample.fieldType === 'textarea'"
                        v-model="currentExample.content"
                        class="w-full mb-4 text-sm"
                        :rows="currentExample.isCode ? 15 : 5"
                        :class="{ 'code-textarea': currentExample.isCode }"
                    />

                    <div v-else-if="currentExample.fieldType === 'text'" class="example-preview">
                        <InputText
                            v-model="currentExample.content"
                            class="w-full text-sm"
                            autocomplete="off"
                        />
                    </div>

                    <div
                        v-else-if="currentExample.fieldType === 'markdown'"
                        class="example-preview"
                    >
                        <MarkdownEditor v-model="currentExample.content" height="500px" />
                    </div>
                </div>
            </div>

            <template #footer>
                <Button
                    label="取消"
                    size="small"
                    icon="pi pi-times"
                    class="p-button-text p-button-danger text-xs"
                    @click="closeExampleDialog"
                />
                <Button
                    v-if="false"
                    label="编辑并应用"
                    size="small"
                    rounded
                    icon="pi pi-pencil"
                    class="p-button-outlined text-xs"
                    :pt="{
                        label: {
                            style: {
                                color: '#005aff'
                            }
                        }
                    }"
                    @click="applyExample(true)"
                />
                <Button
                    label="应用"
                    size="small"
                    rounded
                    icon="pi pi-check"
                    class="text-xs"
                    :pt="{
                        root: {
                            style: {
                                backgroundColor: '#005aff',
                                color: '#fff'
                            }
                        }
                    }"
                    @click="applyExample(false)"
                />
            </template>
        </Dialog>
    </ServerLayout>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useSubmitStore } from '@/stores/submit-store.js';
import { useHomeStore } from '@/stores/home-store.js';
import { useMyServersStore } from '@/stores/my-servers-store.js';
import { useForm } from '@/composables/useForm';
import { formFields as originalFormFields } from './js/form-fields';
import ServerLayout from './components/layout.vue';
import { toast } from '@/utils/toast';
import { formatDateTime } from '@/utils/commonUtils';
import Loading from '@/components/common/Loading.vue';
import MarkdownEditor from '@/components/common/MarkdownEditor.vue';
import { cloneDeep } from 'lodash-es';

const submitStore = useSubmitStore();
const homeStore = useHomeStore();
const myServersStore = useMyServersStore();
const router = useRouter();
const route = useRoute();
const isEdit = ref(false);
const serverId = ref(null);
// 创建本地响应式formFields副本
const localFormFields = ref(cloneDeep(originalFormFields));

// Logo预览相关状态
const logoLoading = ref(false);
const logoError = ref(false);

// 示例弹窗相关状态
const exampleDialogVisible = ref(false);
const selectedProtocolIndex = ref(0);
const selectedPlatformIndex = ref(0);
const selectedPlatformContent = ref('');
const currentExample = ref({
    fieldName: '',
    title: '',
    description: '',
    content: '',
    fieldType: 'text',
    isCode: false,
    platforms: null
});

// 选择协议类型
const selectProtocol = index => {
    selectedProtocolIndex.value = index;
    selectedPlatformIndex.value = 0; // 重置平台选择
    updateSelectedContent();
};

// 选择平台
const selectPlatform = index => {
    selectedPlatformIndex.value = index;
    updateSelectedContent();
};

// 更新选中的内容
const updateSelectedContent = () => {
    if (
        currentExample.value.platforms &&
        currentExample.value.platforms[selectedProtocolIndex.value] &&
        currentExample.value.platforms[selectedProtocolIndex.value].platforms[
            selectedPlatformIndex.value
        ]
    ) {
        selectedPlatformContent.value =
            currentExample.value.platforms[selectedProtocolIndex.value].platforms[
                selectedPlatformIndex.value
            ].content;
    }
};

// 打开示例弹窗
const openExampleDialog = field => {
    currentExample.value = {
        fieldName: field.name,
        title: field.exampleTitle || `${field.label}填写示例`,
        description: field.exampleDescription || `${field.label}的标准填写格式`,
        content: field.exampleData || '',
        fieldType: field.type,
        isCode: field.isCode || false,
        platforms: field.platforms || null
    };

    // 重置选择状态
    selectedProtocolIndex.value = 0;
    selectedPlatformIndex.value = 0;

    // 如果有平台特定的内容，初始化选中内容
    if (currentExample.value.platforms && currentExample.value.platforms.length > 0) {
        updateSelectedContent();
    }

    exampleDialogVisible.value = true;
};

// 关闭示例弹窗
const closeExampleDialog = () => {
    exampleDialogVisible.value = false;
};

// 应用示例内容到表单
const applyExample = isEdited => {
    // 如果是平台特定的内容，使用当前选中的平台内容
    if (currentExample.value.platforms && currentExample.value.platforms.length > 0) {
        formData[currentExample.value.fieldName] = selectedPlatformContent.value;
    } else {
        // 不管是否编辑过，都直接应用当前示例内容
        formData[currentExample.value.fieldName] = currentExample.value.content;
    }

    toast.success(
        `示例已应用至"${getFieldLabelByName(currentExample.value.fieldName)}"`,
        '应用成功'
    );

    closeExampleDialog();
};

// 根据字段名获取字段标签
const getFieldLabelByName = fieldName => {
    const field = localFormFields.value.find(formField => formField.name === fieldName);
    return field ? field.label : fieldName;
};

// Logo URL变化处理
const handleLogoUrlChange = () => {
    // 重置错误状态
    logoError.value = false;

    // 只要有URL，就显示图片预览
    if (formData.logoUrl && formData.logoUrl.trim()) {
        logoLoading.value = true;
    } else {
        logoLoading.value = false;
    }
};

// 来源变化处理 - 当选择汽车之家时自动填充logo，切换到其他来源时清空
const handleSourceChange = () => {
    const autoHomeLogoUrl =
        'https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/logo-s-blue-m.png';

    // 如果来源选择为"汽车之家"（值为1），自动填充logoUrl
    if (formData.source === 1) {
        // 只有当logoUrl为空或者不是汽车之家的logo时才自动填充
        if (!formData.logoUrl || formData.logoUrl !== autoHomeLogoUrl) {
            formData.logoUrl = autoHomeLogoUrl;
            // 触发logo预览更新
            handleLogoUrlChange();
        }
    } else {
        // 如果切换到其他来源，且当前logoUrl是汽车之家的默认logo，则清空
        if (formData.logoUrl === autoHomeLogoUrl) {
            formData.logoUrl = '';
            // 触发logo预览更新
            handleLogoUrlChange();
        }
    }
};

// Logo加载成功处理
const handleLogoLoad = () => {
    logoLoading.value = false;
    logoError.value = false;
};

// Logo加载失败处理
const handleLogoError = () => {
    logoLoading.value = false;
    logoError.value = true;
};

// 填充表单数据
const fillFormData = data => {
    if (!data) {
        return;
    }

    // 遍历数据中的字段，填充表单
    Object.keys(data).forEach(key => {
        if (key in formData) {
            // 特殊处理serverCategories字段，将对象数组转为id数组
            if (key === 'serverCategories' && Array.isArray(data[key])) {
                formData[key] = data[key].map(item => item.id);
            } else if (key === 'programLanguage' && data[key]) {
                // 特殊处理programLanguage字段，确保值与选项匹配
                const programField = localFormFields.value.find(
                    field => field.name === 'programLanguage'
                );
                if (programField && programField.options) {
                    const validOptions = programField.options.map(option => option.value);

                    // 如果后端返回的值不在选项列表中，动态添加这个选项
                    if (!validOptions.includes(data[key])) {
                        console.warn(
                            `后端返回的编程语言 "${data[key]}" 不在预设选项中，动态添加该选项`
                        );
                        programField.options.push({
                            label: data[key], // 使用返回的值作为显示名称
                            value: data[key]
                        });
                    }

                    // 设置formData的值
                    formData[key] = data[key];
                }
            } else {
                formData[key] = data[key];
            }
        }
    });
};

// 初始化server类别数据
const initCategoriesOptions = async () => {
    try {
        // 获取server类别数据
        await myServersStore.getServerCategories();

        // 将server类别数据转换为下拉选项格式
        if (myServersStore.serverCategories && myServersStore.serverCategories.length > 0) {
            const categoryField = localFormFields.value.find(
                field => field.name === 'serverCategories'
            );
            if (categoryField) {
                categoryField.options = myServersStore.serverCategories.map(category => ({
                    label: category.name,
                    value: category.id
                }));
            }
        }
    } catch (error) {
        console.error('获取server类别失败', error);
    }
};

// 重置为原始数据
const resetToOriginal = () => {
    if (isEdit.value && serverId.value) {
        // 重新获取服务器详细信息并填充表单
        myServersStore.getMcpServerDetail({ id: serverId.value }).then(() => {
            fillFormData(myServersStore.serverDetail);
            toast.success('数据已恢复', '已恢复');
        });
    }
};

// 提交处理函数修改
const handleFormSubmit = async formData => {
    try {
        // 创建一个新对象以避免修改原始formData
        const submitData = { ...formData };

        // 处理serverCategories字段与接口参数serverCategoryIds不一致的问题
        if (submitData.serverCategories) {
            submitData.serverCategoryIds = submitData.serverCategories;
            // 删除原始字段，避免同时提交两个字段
            delete submitData.serverCategories;
        }

        // 确保programLanguage字段有值
        if (!submitData.programLanguage) {
            throw new Error('请选择编程语言');
        }

        submitData.submitter = await homeStore.getUsername();

        // 根据场景选择调用不同的接口
        if (isEdit.value && serverId.value) {
            // 编辑模式：调用更新接口
            submitData.id = serverId.value;
            await myServersStore.updateMcpServer(submitData);

            toast.success(`"${submitData.name}" 更新成功，审核通过后将正式上线`, '更新成功');
        } else {
            // 创建模式
            // console.log('submitData', submitData);
            await myServersStore.addMcpServer(submitData);

            toast.success(
                `"${submitData.name}" 创建成功，当前进入审核流程，审核通过后将正式上线`,
                '创建成功',
                '20000'
            );
        }
        navigateToMyServers();
        return true;
    } catch (error) {
        console.error('提交失败', error);
        throw error;
    }
};

// 使用表单hook，传入localFormFields而不是formFields
const { formData, errors, loading, submit } = useForm({
    fields: localFormFields.value,
    submitHandler: handleFormSubmit
});

// 监听来源字段变化，自动填充logo
watch(
    () => formData.source,
    newValue => {
        handleSourceChange();
    },
    { immediate: false }
);

// 导航到server列表页
const navigateToMyServers = () => {
    router.push('/my-servers');
};

// 返回上一页
const navigateBack = () => {
    // 如果是从提交页面来的，返回到提交页面
    if (route.query.gitExtracted) {
        router.push('/submit');
    } else {
        // 否则使用浏览器的返回功能
        router.go(-1);
    }
};

// 初始化
onMounted(async () => {
    // 初始化server类别数据
    await initCategoriesOptions();

    // 处理 Git URL 字段的可编辑状态
    const gitExtracted = route.query.gitExtracted;

    // 找到 gitUrl 字段并设置其 disabled 状态
    const gitUrlField = localFormFields.value.find(field => field.name === 'gitUrl');
    if (gitUrlField) {
        // 如果成功提取了 Git 信息，则禁用编辑；否则允许编辑
        gitUrlField.disabled = gitExtracted === 'true';
    }

    if (route.query.id) {
        isEdit.value = true;
        serverId.value = route.query.id;

        // 获取server详情数据
        await myServersStore.getMcpServerDetail({ id: serverId.value });

        fillFormData(myServersStore.serverDetail);
        return;
    }

    // 检查是否有从submit页面传递的数据
    if (submitStore.repoData) {
        fillFormData(submitStore.repoData);
    }
});

// 滚动到指定区域
const scrollToSection = (sectionId, event) => {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ behavior: 'smooth' });

        // 更新激活状态
        const anchors = document.querySelectorAll('.anchor-item');
        anchors.forEach(anchor => {
            anchor.classList.remove('active');
        });

        // 找到并激活当前点击的项
        if (event && event.currentTarget) {
            event.currentTarget.classList.add('active');
        }
    }
};

// 根据是否必填筛选表单字段
const getFieldsByRequired = isRequired => {
    return localFormFields.value.filter(field => field.required === isRequired);
};
</script>

<style scoped lang="scss">
.page-content {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    border-bottom: 1px solid var(--surface-border, #e6e8eb);
    padding-bottom: 10px;

    .header-left {
        display: flex;
        align-items: center;
        width: 60px;

        .back-btn {
            width: 40px;
            height: 40px;
            color: var(--primary-color);
            transition: all 0.2s ease;

            &:hover {
                background-color: var(--surface-100);
                transform: translateX(-2px);
            }

            .pi {
                font-size: 1.2rem;
            }
        }
    }

    .header-center {
        flex: 1;
        padding: 0 16px;
        position: relative;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background-color: var(--primary-color);
            border-radius: 0 2px 2px 0;
        }

        .title-wrapper {
            display: flex;
            flex-direction: column;
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .title-prefix {
                color: var(--text-color);
                margin-right: 6px;
                font-size: 16px;
            }

            .status-badge {
                margin-left: 8px;
                font-size: 12px;
            }
        }

        .content-subtitle {
            color: var(--text-color-secondary, #6c757d);
            font-size: 12px;
            margin: 0 0 0 10px;
        }
    }

    .header-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-left: 16px;
        max-width: 320px;
    }
}

.anchor-navigation {
    width: 100%;
    flex-shrink: 0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.anchor-list {
    display: flex;
    flex-direction: row;
    gap: 16px;
    padding: 12px 0;
    justify-content: center;
}

.anchor-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;

    .anchor-icon {
        color: #fff;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.form-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.form-section {
    background-color: #fff;
    border-radius: 12px;
    border: 1px solid var(--surface-border, #e6e8eb);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.03);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    }
}

.form-section-header {
    padding: 14px 30px;
    background-color: var(--surface-50, #f8fafc);
    border-bottom: 1px solid var(--surface-border, #e6e8eb);
    display: flex;
    align-items: center;

    .header-icon {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
        font-size: 14px;
    }

    .header-text {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .section-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color, #333);
        margin: 4px 0;
    }

    .section-desc {
        color: var(--text-color-secondary, #6c757d);
        font-size: 12px;
        margin: 0;
    }
}

.form-section-content {
    padding: 16px;
}

.form-field {
    margin-bottom: 16px;
    padding: 12px 16px;
    position: relative;

    &:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }
}

.form-field-column {
    display: flex;
    flex-direction: column;
}

.form-label {
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color, #333);
    text-align: left;
    font-size: 14px;
    display: flex;
    align-items: center;

    &::before {
        content: attr(data-index);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        font-size: 10px;
        margin-right: 8px;
    }
}

.form-field-input {
    width: 100%;
    position: relative;
}

.required-mark {
    color: var(--red-500, #ef4444);
    margin-left: 4px;
}

.field-hint {
    color: var(--text-color-secondary, #6c757d);
    font-size: 12px;
    margin-left: 20px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* 填写示例按钮样式 */
.example-btn {
    width: fit-content;
    font-size: 12px;
    color: white;
    border-radius: 4px;
    padding: 0.3rem 0.6rem;
    background-color: var(--primary-color);
    margin-left: auto;

    .pi {
        font-size: 10px;
        margin-right: 4px;
    }
}

.form-actions {
    margin-top: 24px;
    margin-bottom: 32px;
}

.action-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    max-width: 600px;
    margin: 0 auto;

    :deep(.p-button) {
        width: fit-content;
    }
}

.language-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-left: -10px;
    width: 100%;

    :deep(.p-radiobutton-box) {
        border-color: $system-blue;
    }
    :deep(.p-radiobutton-icon) {
        background-color: $system-blue;
    }
}

.language-option {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    border-radius: 6px;
}

/* 示例弹窗内容样式 */
.example-dialog-content {
    .example-description {
        color: var(--text-color-secondary);
        margin-bottom: 14px;
        font-size: 12px;
        padding: 10px 12px;
        background-color: var(--surface-50);
        border-left: 3px solid var(--primary-color);
        border-radius: 4px;
    }

    .example-selector-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-bottom: 10px;

        .selectors-row {
            display: flex;
            gap: 24px;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .protocol-selector,
        .platform-selector {
            display: flex;
            align-items: center;
            gap: 8px;

            .selector-label {
                font-weight: 500;
                color: $label-primary;
            }

            .protocol-buttons,
            .platform-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
            }
        }

        .code-display-area {
            .code-textarea {
                font-size: 12px;
            }
        }
    }
}

.server-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
    display: inline-block;
}

.server-metadata {
    display: flex;
    flex-direction: column;
    gap: 8px;
    font-size: 12px;
    color: var(--text-color-secondary);
    background-color: var(--surface-50);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid var(--surface-200);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);

    .metadata-item {
        display: flex;
        align-items: center;
        gap: 6px;

        i {
            font-size: 14px;
            color: var(--primary-color);
        }
    }
}

.code-textarea {
    font-family: monospace;
}

/* Logo预览样式 */
.logo-preview-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
}

.logo-preview-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 120px;
    border: 2px dashed var(--surface-border, #e6e8eb);
    border-radius: 8px;
    background-color: var(--surface-50, #f8fafc);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
        border-color: var(--primary-color);
        background-color: var(--surface-100, #f1f3f4);
    }
}

.logo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-color-secondary, #6c757d);

    i {
        font-size: 2rem;
    }

    .placeholder-text {
        font-size: 12px;
        font-weight: 500;
    }
}

.logo-display {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.logo-preview-image {
    max-width: 100%;
    max-height: 100px;
    object-fit: contain;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
        transform: scale(1.05);
    }
}

.logo-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 8px;
    backdrop-filter: blur(4px);

    i {
        font-size: 1.2rem;
    }

    span {
        font-size: 12px;
        font-weight: 500;
    }
}

.logo-error {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: var(--red-50, #fef2f2);
    border: 1px solid var(--red-200, #fecaca);
    border-radius: 6px;
    margin-top: 8px;

    i {
        font-size: 14px;
    }

    .error-text {
        font-size: 12px;
        color: var(--red-600, #dc2626);
        font-weight: 500;
    }
}

/* PrimeVue组件样式优化 */
:deep(.p-dropdown) {
    border-radius: 6px;
}

:deep(.p-inputtext) {
    border-radius: 6px;
    font-size: 14px;

    &::placeholder {
        color: rgba(0, 0, 0, 0.25);
    }
}

:deep(.p-button) {
    border-radius: 6px;

    &.p-button-rounded {
        border-radius: 2rem;
    }
}

:deep(.p-multiselect) {
    border-radius: 6px;
}

:deep(.p-card) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.p-inline-message),
:deep(.p-inline-message .p-inline-message-text) {
    font-size: 12px !important;
}

:deep(.p-inline-message .p-inline-message-icon) {
    font-size: 12px !important;
    margin-right: 3px;
}
</style>
