<template>
    <ServerLayout>
        <!-- 顶部标题区域 -->
        <template #header>
            <div class="header-container">
                <div class="header-title-section">
                    <h2 class="content-title">我的 MCP Servers</h2>
                </div>
            </div>
        </template>

        <!-- 主要内容区域 -->
        <div class="content-container">
            <!-- 数据概览卡片 -->
            <div class="dashboard-cards">
                <div class="dashboard-card">
                    <div class="card-icon info">
                        <i class="pi pi-check-circle"></i>
                    </div>
                    <div class="card-content">
                        <span class="card-label">审核中：</span>
                        <span class="card-value">{{ getStatusCount(1) }} 个</span>
                    </div>
                </div>
                <div class="dashboard-card">
                    <div class="card-icon success">
                        <i class="pi pi-check-circle"></i>
                    </div>
                    <div class="card-content">
                        <span class="card-label">审核通过：</span>
                        <span class="card-value">{{ getStatusCount(2) }} 个</span>
                    </div>
                </div>
                <div class="dashboard-card">
                    <div class="card-icon danger">
                        <i class="pi pi-times-circle"></i>
                    </div>
                    <div class="card-content">
                        <span class="card-label">审核失败：</span>
                        <span class="card-value">{{ getStatusCount(3) }} 个</span>
                    </div>
                </div>
            </div>

            <!-- 服务器列表卡片 -->
            <div class="servers-card">
                <!-- 服务器列表 -->
                <DataTable
                    :value="filteredServers"
                    :paginator="true"
                    :rows="5"
                    :rowsPerPageOptions="[5, 10, 20]"
                    tableStyle="min-width: 100%"
                    class="server-table"
                    size="small"
                    :loading="loading"
                    :pt="{
                        root: {
                            style: {
                                fontSize: '14px',
                                minHeight: '400px',
                            },
                        },
                        loadingOverlay: {
                            style: {
                                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            },
                        },
                    }"
                >
                    <template #loading>
                        <div class="loading-wrapper">
                            <Loading text="加载中..." />
                        </div>
                    </template>

                    <Column field="logoUrl" header="头像" style="width: 70px">
                        <template #body="{ data }">
                            <div class="avatar-container">
                                <span v-if="!data.logoUrl" class="server-icon">
                                    <i class="pi pi-book"></i>
                                </span>
                                <img v-else :src="data.logoUrl" class="server-avatar" alt="服务器头像"/>
                            </div>
                        </template>
                    </Column>
                    <Column field="name" header="名称" style="max-width: 150px">
                        <template #body="{ data }">
                            <div
                                class="copy-text"
                                @click="copyToClipboard(data.name)"
                                :title="data.name"
                            >
                                {{ data.name }}
                            </div>
                        </template>
                    </Column>
                    <Column field="description" header="描述" style="max-width: 200px">
                        <template #body="{ data }">
                            <div
                                class="copy-text"
                                @click="copyToClipboard(data.description)"
                                :title="data.description"
                            >
                                {{ data.description }}
                            </div>
                        </template>
                    </Column>
                    <Column field="auditStatus" header="状态" style="width: 130px">
                        <template #body="{ data }">
                            <div class="status-container">
                                <Tag
                                    :value="getStatusText(data.auditStatus)"
                                    :severity="getStatusSeverity(data.auditStatus)"
                                />
                                <i
                                    v-if="data.auditStatus === 3 && data.auditFailPrompt"
                                    v-tooltip.top="data.auditFailPrompt"
                                    class="pi pi-info-circle ml-2 fail-reason-icon"
                                ></i>
                            </div>
                        </template>
                    </Column>
                    <Column field="modifiedStime" header="更新时间" sortable>
                        <template #body="{ data }">
                            {{ formatDateTime(data.modifiedStime) }}
                        </template>
                    </Column>
                    <Column header="操作">
                        <template #body="{ data }">
                            <div class="action-buttons">
                                <Button
                                    v-tooltip.top="'查看详情'"
                                    icon="pi pi-eye"
                                    class="p-button-text p-button-rounded"
                                    @click="navigateToServerDetail(data)"
                                    size="small"
                                />
                                <Button
                                    v-tooltip.top="'编辑'"
                                    icon="pi pi-pencil"
                                    class="p-button-text p-button-rounded"
                                    @click="navigateToServerEdit(data)"
                                    size="small"
                                />
                                <Button
                                    v-if="data.gitUrl"
                                    v-tooltip.top="'Git仓库'"
                                    icon="pi pi-github"
                                    class="p-button-text p-button-rounded"
                                    @click="openGitUrl(data.gitUrl)"
                                    size="small"
                                />
                            </div>
                        </template>
                    </Column>

                    <!-- 当数据为空时的缺省状态（接口已返回） -->
                    <template #empty>
                        <div v-if="!loading" class="empty-state">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/default/search.png"
                                alt="无数据"
                                class="empty-image"
                            />
                            <p class="empty-text">
                                {{
                                    searchKeyword
                                        ? '没有找到匹配的MCP Server'
                                        : '暂无MCP Server数据'
                                }}
                            </p>
                            <Button
                                icon="pi pi-refresh"
                                label="刷新"
                                class="refresh-button p-button-outlined"
                                :loading="loading"
                                @click="getMyServerList"
                            />
                        </div>
                    </template>
                </DataTable>
            </div>
        </div>
    </ServerLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import ServerLayout from './components/layout.vue';
import { useMyServersStore } from '@/stores/my-servers-store.js';
import { formatDateTime } from '@/utils/commonUtils';
import { useHomeStore } from '@/stores/home-store.js';
import Loading from '@/components/common/Loading.vue';
import { useToast } from 'primevue/usetoast';

const router = useRouter();
const myServersStore = useMyServersStore();
const homeStore = useHomeStore();
const toast = useToast();

// 删除分页参数
const loading = ref(true);
const searchKeyword = ref('');

// 复制文本到剪贴板
const copyToClipboard = text => {
    if (!text) {
        return;
    }

    navigator.clipboard
        .writeText(text)
        .then(() => {
            toast.add({
                severity: 'success',
                summary: '复制成功',
                detail: '内容已复制到剪贴板',
                life: 3000
            });
        })
        .catch(err => {
            console.error('复制失败:', err);
            toast.add({
                severity: 'error',
                summary: '复制失败',
                detail: '请手动复制内容',
                life: 3000
            });
        });
};

// 打开Git链接
const openGitUrl = url => {
    if (!url) {
        return;
    }
    window.open(url, '_blank');
};

// 计算根据搜索关键词过滤后的服务器列表
const filteredServers = computed(() => {
    if (!searchKeyword.value || !myServersStore.myServerData || !myServersStore.myServerData.list) {
        return myServersStore.myServerData?.list || [];
    }

    const keyword = searchKeyword.value.toLowerCase();
    return myServersStore.myServerData.list.filter(
        server =>
            server.name.toLowerCase().includes(keyword) ||
            (server.description && server.description.toLowerCase().includes(keyword)) ||
            (server.gitUrl && server.gitUrl.toLowerCase().includes(keyword))
    );
});

// 获取特定状态的服务器数量
const getStatusCount = status => {
    if (!myServersStore.myServerData || !myServersStore.myServerData.list) {
        return 0;
    }
    return myServersStore.myServerData.list.filter(server => server.auditStatus === status).length;
};

// 获取状态标签的文本
const getStatusText = status => {
    switch (status) {
        case 1:
            return '审核中';
        case 2:
            return '审核通过';
        case 3:
            return '审核失败';
        default:
            return '未知状态';
    }
};

// 获取状态标签的样式
const getStatusSeverity = status => {
    switch (status) {
        case 1:
            return 'info';
        case 2:
            return 'success';
        case 3:
            return 'warning';
        default:
            return 'info';
    }
};

// 获取服务器列表
const getMyServerList = async () => {
    loading.value = true;
    const submitter = await homeStore.getUsername();
    try {
        const params = {
            submitter,
            isIgnoreAuditStatus: true // 忽略审核状态
        };
        await myServersStore.getMcpServerList(params);
    } catch (error) {
        console.error('获取服务器列表失败', error);
    } finally {
        loading.value = false;
    }
};

// 跳转到服务器详情页
const navigateToServerDetail = data => {
    router.push(`/servers/detail/${data.id}`);
};

// 跳转到服务器编辑页
const navigateToServerEdit = data => {
    router.push(`/server-edit?id=${data.id}`);
};

// 初始化获取数据
onMounted(() => {
    getMyServerList();
});
</script>

<style scoped lang="scss">
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-title-section {
    display: flex;
    flex-direction: column;
}

.content-title {
    font-size: $heading-4;
    font-weight: $font-weight-semibold;
    color: $label-primary;
    margin: 0;
}

.content-subtitle {
    color: $label-secondary;
    font-size: $font-size-sm;
    margin: 0;
}

.submit-button {
    background-color: $system-blue;
    border-color: $system-blue;
    border-radius: 8px;
    padding: 8px 10px;
    font-weight: $font-weight-medium;
    font-size: $font-size-sm;
    transition: all 0.2s;
}

.content-container {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.dashboard-cards {
    padding-top: 20px;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.dashboard-card {
    flex: 1;
    min-width: 200px;
    background-color: $system-background-primary;
    border-radius: 12px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;

    i {
        font-size: $font-size-xl;
        color: white;
    }

    &.info {
        background-color: rgba($system-blue, 0.1);

        i {
            color: $system-blue;
        }
    }

    &.success {
        background-color: rgba($system-green, 0.1);

        i {
            color: $system-green;
        }
    }

    &.danger {
        background-color: rgba($system-red, 0.1);

        i {
            color: $system-red;
        }
    }
}

.card-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-label {
    color: $label-primary;
    font-size: 14px;
    font-weight: $font-weight-medium;
}

.card-value {
    font-size: 14px;
    font-weight: $font-weight-semibold;
    color: $label-primary;
}

.servers-card {
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    padding: 6px 20px;
    background-color: #fff;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.card-header {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $fill-color-tertiary;
    background-color: $system-background-primary;
}

.card-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: $label-primary;
    margin: 0;
}

.card-actions {
    display: flex;
    gap: 12px;
}

.search-input {
    width: 250px;
}

.server-avatar {
    width: 32px;
    height: 32px;
}

.server-icon {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $fill-color-tertiary;
    border-radius: 6px;
    color: $label-secondary;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: flex;
    gap: 8px;
    :deep(.p-button) {
        color: $system-blue;
    }
}

.status-container {
    display: flex;
    align-items: center;
}

.fail-reason-icon {
    color: $system-red;
    cursor: pointer;
    font-size: 1rem;
    transition: color 0.2s;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    background-color: $system-background-primary;
    border-radius: 12px;
    height: 400px;

    .empty-image {
        width: 160px;
        margin-bottom: 16px;
        opacity: 0.8;
        transition: all 0.3s ease;

        &:hover {
            transform: scale(1.05);
        }
    }
}

.empty-text {
    color: $label-secondary;
    font-size: $font-size-base;
    margin: 0 0 20px 0;
    font-weight: $font-weight-medium;
}

.refresh-button {
    transition: all 0.2s ease;
    border-radius: 8px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.copy-text {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding-right: 30px;
}

:deep(.server-table) {
    flex: 1;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable-wrapper) {
    flex: 1;
}

:deep(.p-datatable-thead > tr > th) {
    padding: 20px 10px !important;
}
</style>
