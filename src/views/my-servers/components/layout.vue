<template>
    <div class="personal-center-container">
        <!-- 左侧导航 -->
        <div class="left-nav">
            <!-- 导航分组：账户 -->
            <div class="nav-group">
                <div class="nav-group-header" @click="toggleAccountSection">
                    <div class="nav-group-header-left">
                        <i class="pi pi-user nav-icon"></i>
                        <span class="nav-group-header-title">个人中心</span>
                    </div>
                    <div class="nav-group-header-right">
                        <i
                            class="pi expand-icon"
                            :class="accountSectionExpanded ? 'pi-chevron-down' : 'pi-chevron-right'"
                        ></i>
                    </div>
                </div>

                <div v-if="accountSectionExpanded" class="nav-items">
                    <div
                        class="nav-item"
                        :class="{
                            active: activeTab === 'my-servers'
                        }"
                        @click="navigateToMyServers"
                    >
                        <span>我的 MCP Server</span>
                    </div>
                    <div
                        
                        class="nav-item"
                        :class="{
                            active: activeTab === 'my-apikeys'
                        }"
                        @click="navigateToApiKeys"
                    >
                        <span>API Keys</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-content">
            <!-- 顶部白色header -->
            <div v-if="$slots.header" class="page-header">
                <slot name="header"></slot>
            </div>

            <!-- 主要内容区域 -->
            <div class="content">
                <!-- 检查是否有子路由 项目中暂时没有这种子路由 -->
                <router-view v-if="hasChildRoute"></router-view>
                <!-- 没有子路由时显示默认内容 -->
                <slot v-else></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const activeTab = ref('my-servers');
const accountSectionExpanded = ref(true);

// 判断是否有子路由
const hasChildRoute = computed(() => {
    return route.path !== '/my-servers' && route.path.startsWith('/my-servers');
});

// 切换账户部分展开/收起
const toggleAccountSection = () => {
    accountSectionExpanded.value = !accountSectionExpanded.value;
};

// 根据路由路径设置激活的标签页
const setActiveTabByPath = path => {
    if (path.includes('my-apikeys')) {
        activeTab.value = 'my-apikeys';
    } else if (
        path.includes('my-servers') ||
        path.includes('submit') ||
        path.includes('server-edit')
    ) {
        // /my-servers, /submit, /server-edit 都属于 "我的 MCP Server" 范畴
        activeTab.value = 'my-servers';
    } else {
        // 默认情况
        activeTab.value = 'my-servers';
    }
};

// 监听路由变化，实时更新tab状态
watch(
    () => route.path,
    newPath => {
        setActiveTabByPath(newPath);
    },
    { immediate: true } // 立即执行一次，相当于在onMounted中执行
);

// 导航到我的服务器列表
const navigateToMyServers = () => {
    router.push('/my-servers');
};

// 导航到API Keys页面
const navigateToApiKeys = () => {
    router.push('/my-apikeys');
};
</script>

<style scoped lang="scss">
.personal-center-container {
    display: flex;
    min-height: 100%;
}

// 左侧导航栏
.left-nav {
    width: 280px;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

// 导航分组
.nav-group {
    .nav-group-header {
        margin: 20px auto 10px;
        width: 200px;
        height: 44px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $system-blue;
        font-weight: $font-weight-medium;
        font-size: $font-size-base;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.08);
        padding: 0 16px;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.12);
            transform: translateY(-1px);
        }

        .nav-icon {
            font-size: $font-size-base;
            color: $system-blue;
        }

        .expand-icon {
            font-size: $font-size-base;
            color: $system-blue;
            transition: transform 0.3s ease;
        }

        .nav-group-header-title {
            margin-left: 10px;
            font-weight: 600;
        }
    }
}

// 导航项
.nav-items {
    margin-top: 8px;
    transition: all 0.3s ease;
}

.nav-item {
    width: 200px;
    height: 40px;
    margin: 4px auto;
    display: flex;
    padding-left: 20px;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: $label-secondary;
    font-size: $font-size-base;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        width: 4px;
        height: 0;
        background: $system-blue;
        transition: height 0.3s ease;
        border-radius: 0 4px 4px 0;
    }

    &:hover {
        color: $system-blue;
        background-color: rgba(235, 245, 255, 0.7);

        &::before {
            height: 20px;
        }
    }

    &.active {
        color: $system-blue;
        font-weight: $font-weight-medium;
        background-color: rgba(225, 240, 255, 0.7);

        &::before {
            height: 30px;
        }
    }
}

// 右侧内容区
.right-content {
    margin: 20px 0 0 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 12px;

    &.nav-collapsed {
        margin-left: 60px;
    }
}

.page-header {
    padding: 0 20px;
}

.content {
    flex: 1;
    padding: 0 20px 0;
    overflow-y: auto;
}
</style>
