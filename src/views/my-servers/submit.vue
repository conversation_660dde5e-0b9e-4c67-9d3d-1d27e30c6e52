<template>
    <div class="submit-page-container">
        <div class="submit-page-content">
            <h1 class="submit-page-title">提交你的 MCP Server</h1>
            <p class="submit-page-subtitle">共建技术生态，赋能业务创新</p>

            <div class="submit-form-card">
                <form class="p-fluid" @submit.prevent="extractGitInfo">
                    <div v-for="field in visibleFormFields" :key="field.name" class="form-field">
                        <div class="form-field-row align-items-center gap-4">
                            <label :for="field.name" class="form-label text-sm">
                                {{ field.label }}：
                                <span v-if="field.required" class="required-mark">*</span>
                            </label>

                            <div class="form-field-input flex flex-wrap align-items-center gap-2">
                                <!-- 根据字段类型渲染不同的输入组件 -->
                                <template v-if="field.type === 'dropdown'">
                                    <Dropdown
                                        :id="field.name"
                                        v-model="formData[field.name]"
                                        :options="field.options"
                                        option-label="name"
                                        class="w-full"
                                        :class="{ 'p-invalid': errors[field.name] }"
                                    />
                                </template>

                                <template v-else-if="field.type === 'text'">
                                    <InputText
                                        :id="field.name"
                                        v-model="formData[field.name]"
                                        class="w-full text-sm"
                                        :placeholder="field.placeholder"
                                        :class="{ 'p-invalid': errors[field.name] }"
                                        type="text"
                                        autocomplete="off"
                                        :invalid="errors[field.name]"
                                    />
                                </template>

                                <InlineMessage v-if="errors[field.name]" severity="error">
                                    {{ errors[field.name] }}
                                </InlineMessage>
                            </div>
                        </div>
                        <!-- 为GitLab地址字段添加提示信息 -->
                        <p v-if="field.name === 'url'" class="helper-text mt-4 w-full text-xs">
                            <span class="highlight-text">选填</span>：输入 GitLab/Github 仓库的 HTTP
                            地址 <span class="highlight-text">可一键提取信息</span>。若为
                            <span class="highlight-text">GitLab</span> 仓库，务必添加
                            <span class="highlight-text">reviewboard 账号</span>及<span
                                class="highlight-text"
                                >报告者</span
                            >（含）以上权限，且需在<span class="highlight-text">master分支上</span
                            >包含<span class="highlight-text">README.md（区分大小写）</span>文件。
                        </p>
                    </div>

                    <div class="action-buttons-container">
                        <Loading v-if="loading" text="提取中" />
                        <template v-else>
                            <Button
                                class="p-button-outlined skip-button text-sm"
                                rounded
                                label="跳过，手动填写"
                                icon="pi pi-arrow-right"
                                @click="skipToNextStep"
                                type="button"
                            />
                            <Button
                                class="extract-button text-sm"
                                rounded
                                label="一键提取Git信息"
                                icon="pi pi-download"
                                type="submit"
                                :disabled="!formData.url"
                            />
                        </template>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useForm } from '@/composables/useForm';
import { validators } from '@/utils/validators';
import { useSubmitStore } from '@/stores/submit-store.js';
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';
import { toast } from '@/utils/toast';
import Loading from '@/components/common/Loading.vue';

const submitStore = useSubmitStore();
const router = useRouter();

const loading = ref(false);

// 定义表单字段配置
const formFields = [
    {
        name: 'type',
        label: '类型',
        type: 'dropdown',
        required: true,
        defaultValue: { name: 'MCP Server', value: 'server' },
        options: [{ name: 'MCP Server', value: 'server' }],
        visible: false, // 隐藏类型选择器
    },
    {
        name: 'url',
        label: 'Git 仓库地址',
        type: 'text',
        required: false,
        placeholder: 'https://gitlab.company.com/username/repo-name',
        validators: [validators.githubUrl],
        visible: true,
    },
];

// 计算可见的表单字段
const visibleFormFields = computed(() => {
    return formFields.filter((field) => field.visible !== false);
});

// 提取Git信息处理函数
const extractGitInfo = async () => {
    // 如果没有填写Git地址，直接跳转到下一步
    if (!formData.url) {
        skipToNextStep();
        return;
    }

    try {
        loading.value = true;

        // 发起请求获取仓库详情
        await submitStore.getRepoDetail({ gitUrl: formData.url });

        // 跳转到编辑页面，标记Git提取成功
        router.push({
            path: '/server-edit',
            query: { gitExtracted: 'true' },
        });

        toast.success('仓库信息已提取');
    } catch (error) {
        console.log(error);

        // 清空之前可能保存的数据
        submitStore.clearRepoData();

        // 即使提取失败也跳转到下一步，但不标记Git提取成功
        router.push({
            path: '/server-edit',
            query: { gitExtracted: 'false' },
        });

        toast.error('Git仓库不符合提取要求，请手动填写信息');
    } finally {
        loading.value = false;
    }
};

// 跳过提取，直接进入下一步
const skipToNextStep = () => {
    // 清空之前可能保存的数据
    submitStore.clearRepoData();

    router.push({
        path: '/server-edit',
        query: { gitExtracted: 'false' },
    });
};

// 使用表单hook
const { formData, errors } = useForm({
    fields: formFields,
    submitHandler: extractGitInfo,
});
</script>

<style lang="scss" scoped>
.submit-page-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 48px 16px;
}

.submit-page-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.submit-page-title {
    font-size: $heading-1;
    font-weight: $font-weight-bold;
    color: $system-blue;
    text-align: center;
    margin-bottom: 12px;
    margin-top: 0;
}

.submit-page-subtitle {
    font-size: $font-size-lg;
    /* color: $label-secondary; */
    text-align: center;
    margin-bottom: 32px;
    max-width: 600px;
}

.submit-form-card {
    width: 80%;
    background-color: $system-background-primary;
    border-radius: 12px;
    padding: 40px 40px 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-field {
    margin-bottom: 32px;
}

.form-field-row {
    display: flex;
}

.form-label {
    font-weight: $font-weight-medium;
    color: $label-primary;
}

.form-field-input {
    flex: 1;
}

.required-mark {
    color: $system-red;
    margin-left: 4px;
}

.helper-text {
    color: $label-secondary;
    font-size: 0.85rem;
    line-height: 1.6;
    margin-top: 10px;
    letter-spacing: 0.3px;
}

.highlight-text {
    font-weight: 500;
    color: $system-blue;
    margin: 0 2px;
}

.action-buttons-container {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 24px;
}

.extract-button {
    background-color: $system-blue;
    width: fit-content;
    letter-spacing: 0.3px;
}

.skip-button {
    color: $system-blue;
    width: fit-content;
    letter-spacing: 0.3px;
}

:deep(.p-inline-message),
:deep(.p-inline-message .p-inline-message-text) {
    font-size: 14px !important;
    line-height: 1.5;
    padding: 3px 5px;
}

:deep(.p-inline-message .p-inline-message-icon) {
    font-size: 14px !important;
    margin-right: 2px;
}

:deep(.p-inputtext::placeholder) {
    color: rgba(0, 0, 0, 0.25); /* 更浅的placeholder颜色 */
}
</style>
