<template>
    <ServerLayout>
        <!-- 顶部标题区域 -->
        <template #header>
            <div class="header-container">
                <div class="header-title-section">
                    <h2 class="content-title">API Keys</h2>
                </div>
            </div>
        </template>

        <!-- 主要内容区域 -->
        <div class="content-container">
            <!-- API密钥信息提示 -->
            <div class="api-key-info">
                <div class="info-card">
                    <div class="info-content">
                        <div class="security-badge">
                            <i class="pi pi-shield"></i>
                            <span>安全提示</span>
                        </div>

                        <div class="info-text">
                            <div class="info-item">
                                <i class="pi pi-exclamation-triangle"></i>
                                <span
                                    >请务必以安全的方式使用 API
                                    密钥，切勿分享或嵌入到公开代码中</span
                                >
                            </div>
                            <div class="info-item special">
                                <i class="pi pi-info-circle"></i>
                                <span>DeepSeek KEY 默认仅用于Cline，请勿用于其他用途</span>
                            </div>
                        </div>

                        <div class="doc-link-container" @click="openDocumentation()">
                            <i class="pi pi-book"></i>
                            <span>Cline 配置及使用文档</span>
                            <i class="pi pi-external-link"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API密钥列表卡片 -->
            <div class="api-keys-card">
                <!-- 加载状态 -->
                <div v-if="apiKeysStore.loading" class="loading-wrapper">
                    <Loading text="加载中..." />
                </div>

                <!-- 错误状态 -->
                <div v-else-if="apiKeysStore.error" class="error-container">
                    <i class="pi pi-exclamation-triangle"></i>
                    <span>{{ apiKeysStore.error }}</span>
                    <Button
                        label="重试"
                        class="p-button-sm p-button-outlined"
                        @click="loadApiKeys"
                    />
                </div>

                <!-- API密钥列表 -->
                <DataTable
                    v-else
                    :value="apiKeys"
                    :paginator="false"
                    tableStyle="min-width: 100%"
                    class="api-keys-table"
                    size="small"
                    :pt="{
                        root: {
                            style: {
                                fontSize: '14px',
                                minHeight: '400px'
                            }
                        }
                    }"
                >
                    <Column
                        field="modelType"
                        header="模型名称"
                        style="width: 120px; min-width: 160px"
                    >
                        <template #body="{ data }">
                            <div class="model-type">
                                {{ data.modelType }}
                            </div>
                        </template>
                    </Column>
                    <Column
                        field="baseUrl"
                        header="Base URL"
                        style="width: 200px; min-width: 160px"
                    >
                        <template #body="{ data }">
                            <div class="copy-text" :title="data.baseUrl">
                                {{ data.baseUrl }}
                            </div>
                        </template>
                    </Column>
                    <Column field="key" header="API Key" style="min-width: 280px">
                        <template #body="{ data }">
                            <div class="key-container">
                                <div class="key-value">
                                    {{ data.key }}
                                </div>
                                <div class="key-actions">
                                    <Button
                                        icon="pi pi-copy"
                                        class="p-button-text p-button-rounded"
                                        @click="
                                            copyToClipboard(
                                                data.key,
                                                'API密钥已复制到剪贴板',
                                                '请手动复制API密钥'
                                            )
                                        "
                                        v-tooltip.top="复制"
                                        size="small"
                                    />
                                </div>
                            </div>
                        </template>
                    </Column>

                    <!-- 表格内置空状态 -->
                    <template #empty>
                        <div class="empty-state">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/default/search.png"
                                alt="无数据"
                                class="empty-image"
                            />
                            <p class="empty-text">暂无API密钥数据</p>
                            <Button
                                icon="pi pi-refresh"
                                label="刷新"
                                class="refresh-button p-button-outlined"
                                :loading="apiKeysStore.loading"
                                @click="loadApiKeys"
                            />
                        </div>
                    </template>
                </DataTable>
            </div>
        </div>
    </ServerLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useApiKeysStore } from '@/stores/api-keys-store';
import { useDocumentNavigation } from '@/composables/useDocumentNavigation';
import ServerLayout from '@/views/my-servers/components/layout.vue';
import Loading from '@/components/common/Loading.vue';

// Store 和 工具
const apiKeysStore = useApiKeysStore();
const toast = useToast();
const { navigateToClineInstall } = useDocumentNavigation();

// 计算属性：将store数据映射为组件需要的格式
const apiKeys = computed(() => {
    return apiKeysStore.apiKeysList.map((item, index) => ({
        id: index + 1, // 生成唯一ID
        modelType: item.model,
        baseUrl: item.baseUrl,
        key: item.apiKey
    }));
});

/**
 * 加载API密钥列表
 */
const loadApiKeys = async () => {
    try {
        await apiKeysStore.getApiKeys();
    } catch (error) {
        // 错误已在store中处理
    }
};

/**
 * 复制文本到剪贴板 - 包含多种备用方案
 * @param {string} text - 要复制的文本
 * @param {string} successMessage - 成功提示消息
 * @param {string} errorMessage - 失败提示消息
 */
const copyToClipboard = async (
    text,
    successMessage = '内容已复制到剪贴板',
    errorMessage = '请手动复制内容'
) => {
    if (!text) {
        return;
    }

    try {
        // 方案1：优先使用现代剪贴板API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            console.log('使用 clipboard API 复制成功');
        }
        // 方案2：备用方案 - 使用传统的 execCommand
        else {
            const successful = fallbackCopyTextToClipboard(text);
            if (!successful) {
                throw new Error('所有复制方法都失败了');
            }
            console.log('使用 execCommand 复制成功');
        }

        // 清除之前的toast，避免堆积
        toast.removeAllGroups();
        toast.add({
            severity: 'success',
            summary: '复制成功',
            detail: successMessage,
            life: 3000
        });
    } catch (err) {
        console.error('复制失败详情:', {
            error: err,
            hasClipboard: !!navigator.clipboard,
            isSecureContext: window.isSecureContext,
            protocol: window.location.protocol,
            text: text ? '有文本数据' : '无文本数据'
        });

        // 清除之前的toast，避免堆积
        toast.removeAllGroups();
        toast.add({
            severity: 'error',
            summary: '复制失败',
            detail: window.isSecureContext ? errorMessage : '需要HTTPS环境才能自动复制，请手动复制',
            life: 5000
        });
    }
};

/**
 * 备用复制方法 - 使用 execCommand
 * @param {string} text - 要复制的文本
 * @returns {boolean} 是否复制成功
 */
const fallbackCopyTextToClipboard = text => {
    try {
        // 创建一个临时的 textarea 元素
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 设置样式，确保元素不可见但仍然可以被选中
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // 尝试执行复制命令
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        return successful;
    } catch (err) {
        console.error('execCommand 复制失败:', err);
        return false;
    }
};

/**
 * 打开Cline配置及使用文档
 */
const openDocumentation = () => {
    // 使用composable中的方法跳转到Cline安装文档，在新窗口打开
    navigateToClineInstall(true);
};

// 组件挂载后加载数据
onMounted(() => {
    loadApiKeys();
});
</script>

<style scoped lang="scss">
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-title-section {
    display: flex;
    flex-direction: column;
}

.content-title {
    font-size: $heading-4;
    font-weight: $font-weight-semibold;
    color: $label-primary;
    margin: 0;
}

.content-subtitle {
    color: $label-secondary;
    font-size: $font-size-sm;
    margin: 0;
}

.content-container {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.api-key-info {
    padding-top: 20px;
    .info-card {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        border-radius: 10px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
            border-radius: 10px 10px 0 0;
        }

        &:hover {
            // transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
    }

    .info-content {
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
    }

    .security-badge {
        display: flex;
        align-items: center;
        gap: 6px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
        flex-shrink: 0;

        i {
            font-size: 12px;
        }
    }

    .info-text {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: 1;
        min-width: 0;

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            line-height: normal;
            color: #374151;
            span {
                font-weight: 600;
            }

            i {
                font-size: 14px;
                flex-shrink: 0;
                width: 16px;
                text-align: center;
            }

            &:first-child {
                i {
                    color: #f59e0b;
                }
            }

            &.special {
                color: $system-blue;
                font-weight: $font-weight-medium;
            }
        }
    }

    .doc-link-container {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        user-select: none;
        color: $system-blue;
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
        font-size: 12px;
        font-weight: 600;
        flex-shrink: 0;

        &:hover {
            background: #3b82f6;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
        }

        i {
            font-size: 12px;
            transition: all 0.3s ease;
        }

        &:hover i {
            transform: scale(1.1);
        }
    }
}

.api-keys-card {
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    padding: 6px 20px;
    background-color: #fff;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px;
    color: $system-red;
    flex-direction: column;

    .pi-exclamation-triangle {
        font-size: 24px;
    }
}

.model-type {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    overflow: hidden;
    // font-weight: $font-weight-medium;
    color: $label-primary;
}

.key-container {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    /* 在小屏幕时优化布局 */
    @media (max-width: 767px) {
        align-items: flex-start;
        gap: 6px;
    }
}

.key-value {
    padding: 6px 12px;
    border-radius: 6px;
    flex: 1;
    min-width: 0;
    max-width: 100%;
    word-break: break-all;
    line-height: 1.4;

    /* 大屏幕时不换行 */
    @media (min-width: 1200px) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 中等屏幕时适当换行 */
    @media (max-width: 1199px) and (min-width: 768px) {
        white-space: normal;
        overflow-wrap: break-word;
    }

    /* 小屏幕时完全支持换行 */
    @media (max-width: 767px) {
        white-space: normal;
        overflow-wrap: break-word;
        word-break: break-all;
    }
}

.key-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
    align-items: center;

    /* 在小屏幕时保持按钮紧凑 */
    @media (max-width: 767px) {
        flex-direction: row;
        align-items: flex-start;
        margin-top: 2px;
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    background-color: $system-background-primary;
    border-radius: 12px;
    height: 400px;

    .empty-image {
        width: 160px;
        margin-bottom: 16px;
        opacity: 0.8;
        transition: all 0.3s ease;

        &:hover {
            transform: scale(1.05);
        }
    }
}

.empty-text {
    color: $label-secondary;
    font-size: $font-size-base;
    margin: 0 0 20px 0;
    font-weight: $font-weight-medium;
}

.refresh-button {
    transition: all 0.2s ease;
    border-radius: 8px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
}

.copy-text {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding-right: 30px;

    /* 在小屏幕时允许换行 */
    @media (max-width: 767px) {
        white-space: normal;
        overflow-wrap: break-word;
        word-break: break-all;
        padding-right: 0;
    }
}

:deep(.api-keys-table) {
    flex: 1;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable-wrapper) {
    flex: 1;
}

:deep(.p-datatable-thead > tr > th) {
    padding: 20px 10px !important;
}

:deep(.p-datatable-tbody > tr > td) {
    padding: 14px 10px !important;

    /* 在小屏幕时调整单元格布局 */
    @media (max-width: 767px) {
        padding: 12px 8px !important;
        vertical-align: top;
    }
}

:deep(.p-button-text) {
    color: $system-blue;
}
</style>
