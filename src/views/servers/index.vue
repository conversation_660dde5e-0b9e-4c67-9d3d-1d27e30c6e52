<template>
    <div class="servers-page">
        <div class="page-content h-full">
            <div class="sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">
                        <i class="pi pi-filter"></i>
                        <span>分类筛选</span>
                    </h3>
                    <div class="sidebar-content">
                        <ServerCategory
                            ref="categoryComponent"
                            :categories="categories"
                            :loading="categoryLoading"
                            :error="categoryError"
                            @category-selected="handleCategorySelected"
                        />
                    </div>
                </div>
            </div>
            <div class="main-content">
                <div class="flex align-items-center mb-4 flex-shrink-0">
                    <div class="flex-1 w-full flex align-items-center">
                        <InputText
                            style="width: 320px"
                            v-model="homeStore.keyword"
                            placeholder="搜索服务器关键词"
                            class="mr-2 text-sm"
                            @input="handleSearch"
                        />
                        <ServerSource ref="sourceComponent" @source-changed="handleSourceChanged" />
                    </div>
                    <ServerSort :initial-sort="sortType" @sort-changed="handleSortChanged" />
                </div>
                <div class="servers-content">
                    <Loading
                        v-if="loading && serversList.length === 0"
                        text="正在加载MCP Server列表..."
                    />
                    <div v-else-if="selectedFilterError" class="state-container error-state">
                        <i class="pi pi-exclamation-triangle"></i>
                        <p>加载失败: {{ selectedFilterError }}</p>
                        <Button
                            label="重试"
                            icon="pi pi-refresh"
                            severity="danger"
                            outlined
                            @click="loadServers"
                        />
                    </div>
                    <!-- 无数据 -->
                    <div v-else-if="serversList.length === 0" class="empty-state-content">
                        <DataStatus
                            :is-empty="true"
                            :status-text="'请尝试清除筛选或重新搜索'"
                            :status-title-visible="true"
                            :status-title="'暂无符合条件的MCP Server'"
                        />
                    </div>

                    <!-- 有数据 -->
                    <div v-else class="server-grid">
                        <div v-for="server in serversList" :key="server.id" class="server-item">
                            <ServerCard :server="server" />
                        </div>
                    </div>
                    <div
                        class="load-more-container"
                        v-if="serversList.length > 0 && totalRecords > pageSize"
                    >
                        <Paginator
                            :rows="pageSize"
                            :total-records="totalRecords"
                            :first="(currentPage - 1) * pageSize"
                            @page="onPageChange"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import { useServerStore } from '@/stores/server-store';
import { useHomeStore } from '@/stores/home-store';
import ServerCard from '@/components/server/server-card.vue';
import ServerCategory from './components/server-category.vue';
import ServerSort from './components/server-sort.vue';
import ServerSource from './components/server-source.vue';
import Loading from '@/components/common/Loading.vue';
import { useRoute } from 'vue-router';
import DataStatus from '@/components/common/data-status.vue';
import Paginator from 'primevue/paginator';
import InputText from 'primevue/inputtext';
import { debounce } from '@/utils/commonUtils';

const route = useRoute();
const serverStore = useServerStore();
const homeStore = useHomeStore();
const loading = computed(() => serverStore.loading);
const serversList = computed(() => serverStore.serversList);
const selectedFilterError = computed(() => serverStore.error);
const currentPage = ref(1);
const pageSize = ref(12);
const serverCategoryId = ref(null);
const sortType = ref(0);
const sourceType = ref(null);
const categoryComponent = ref(null);
const sourceComponent = ref(null);
const totalRecords = ref(0);
const categories = ref([]);
const categoryLoading = ref(false);
const categoryError = ref('');

// 从路由获取初始source
onMounted(() => {
    // 这里 homeStore.keyword 已经是首页带过来的值
    // 直接用它作为初始搜索条件
    if (homeStore.source) {
        sourceType.value = homeStore.source;
        if (sourceComponent.value) {
            sourceComponent.value.selectSource(homeStore.source);
        }
    } else if (route.query.source) {
        sourceType.value = Number(route.query.source);
        if (sourceComponent.value) {
            sourceComponent.value.selectSource(sourceType.value);
        }
    }
    // 加载服务器列表（同时会加载分类数据）
    loadServers();
});

// 监听路由变化，处理刷新请求
watch(
    () => route.query.refresh,
    (newRefresh) => {
        if (newRefresh) {
            // 清空所有筛选条件
            resetAllFilters();
            // 重新加载数据
            loadServers();
        }
    }
);

// 重置所有筛选条件的方法
const resetAllFilters = () => {
    currentPage.value = 1;
    serverCategoryId.value = null;
    sortType.value = 0;
    sourceType.value = null;

    // 重置子组件状态
    categoryComponent.value?.resetCategory();
    sourceComponent.value?.resetSource();
};

// 处理搜索，使用防抖优化
const handleSearch = debounce(() => {
    currentPage.value = 1;
    loadServers();
}, 500);
// 首次加载和分页加载
const loadServers = async () => {
    try {
        serverStore.loading = true;
        serverStore.clearError();
        serverStore.serversList = [];

        // 每次加载服务器列表时都同时加载分类数据
        categoryLoading.value = true;
        categoryError.value = '';

        const params = {
            isIgnoreAuditStatus: false,
            keyword: homeStore.keyword || '',
            pageIndex: currentPage.value,
            pageSize: pageSize.value,
            sortType: sortType.value,
        };

        if (sourceType.value) {
            params.source = sourceType.value;
        }

        if (serverCategoryId.value) {
            params.serverCategoryId = serverCategoryId.value;
        }

        const result = await serverStore.getServers(params);

        // 更新服务器列表数据
        serverStore.serversList = result.list || [];
        totalRecords.value = result.rowcount ?? 0;

        // 每次都更新分类数据
        categories.value = result.serverCategoryAndCounts || [];
        console.log(
            '数据加载成功 - 服务器:',
            serverStore.serversList.length,
            '分类:',
            categories.value.length
        );
    } catch (error) {
        console.error('获取服务器列表失败:', error);
        categoryError.value = '分类加载失败';
        // 错误已在 store 中处理，这里不需要重复设置
    } finally {
        serverStore.loading = false;
        categoryLoading.value = false;
    }
};

// 分页切换
const onPageChange = (event) => {
    currentPage.value = Math.floor(event.first / event.rows) + 1;
    loadServers();
};

// 处理筛选/搜索变更时，重置 currentPage 并加载第一页
const handleCategorySelected = (id) => {
    serverCategoryId.value = id;
    currentPage.value = 1;
    loadServers();
};

const handleSortChanged = (val) => {
    sortType.value = val;
    currentPage.value = 1;
    loadServers();
};

const handleSourceChanged = (val) => {
    sourceType.value = val;
    currentPage.value = 1;
    loadServers();
};

// 组件卸载时清除关键词
onUnmounted(() => {
    homeStore.clearKeyword();
});
</script>

<style scoped lang="scss">
.servers-page {
    color: #1f2937;
    padding-top: 2rem;
    padding-bottom: 1rem;
    height: 100%;
    box-sizing: border-box;
}

.category-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background-color: #f3f4f6;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 500;
    color: #4b5563;
}

.category-badge i {
    font-size: 14px;
    color: #6b7280;
}

/* 页面内容区域 */
.page-content {
    display: flex;
    gap: 32px;
    width: 100%;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    flex-shrink: 0;
}

.sidebar-section {
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #edf2f7;
    transition: box-shadow 0.3s ease, transform 0.3s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
}

.sidebar-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.sidebar-title i {
    font-size: 16px;
    color: var(--primary-color);
}

.sidebar-content {
    margin-top: 16px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    width: 1%;
}

.server-item {
    width: calc(33% - 14px);
    flex-shrink: 0;
}

/* 服务器内容区域 */
.servers-content {
    min-height: 400px;
}

.error-state {
    color: #ef4444;

    p {
        margin: 10px 0 20px;
        font-size: 16px;
        font-weight: 500;
    }
}

.empty-state-content {
    padding-top: 5rem;
}

.empty-image {
    width: 7.5rem;
}

.empty-title {
    margin: 20px 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

.empty-message {
    margin: 0 0 24px 0;
    font-size: 14px;
    color: #6b7280;
    text-align: center;
}

.empty-actions {
    display: flex;
    gap: 0.75rem;
}

.server-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
}

.load-more-btn:deep(.p-button) {
    background: linear-gradient(to right, var(--primary-color), var(--primary-600));
    border: none;
    padding: 12px 24px;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(var(--primary-500-rgb), 0.3);

    &:hover {
        box-shadow: 0 5px 15px rgba(var(--primary-500-rgb), 0.4);
        transform: translateY(-2px);
    }

    &:focus {
        box-shadow: 0 0 0 2px rgba(var(--primary-500-rgb), 0.2);
    }

    &:active {
        transform: translateY(0);
    }

    .p-button-label {
        font-weight: 600;
    }
}
</style>
