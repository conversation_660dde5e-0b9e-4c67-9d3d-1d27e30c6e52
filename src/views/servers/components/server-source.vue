<template>
    <div class="flex align-items-center gap-3 text-sm">
        <SelectButton
            v-model="selectedSource"
            :options="sourceItems"
            option-label="label"
            option-value="value"
            data-key="value"
            :allow-empty="false"
            size="small"
            :pt="{
                label: {
                    class: 'text-sm font-bold',
                },
            }"
            @change="handleSourceChange"
        />
    </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from 'vue';
import { SourceType, getSourceTypeName } from '@/enums/sourceType';

const props = defineProps({
    initialSource: {
        type: [Number, String],
        default: null,
    },
});

const emit = defineEmits(['source-changed']);

// 选中的来源
const selectedSource = ref(props.initialSource);

// 定义来源项
const sourceItems = [
    { label: '全部', value: null },
    { label: getSourceTypeName(SourceType.HOME), value: SourceType.HOME },
    { label: getSourceTypeName(SourceType.OFFICIAL), value: SourceType.OFFICIAL },
    { label: getSourceTypeName(SourceType.THIRD_PARTY), value: SourceType.THIRD_PARTY },
];

/**
 * 处理来源变化
 */
const handleSourceChange = () => {
    emit('source-changed', selectedSource.value);
};

/**
 * 供父组件调用 - 选择来源
 * @param {number|null} value - 来源值
 */
const selectSource = (value) => {
    selectedSource.value = value;
    emit('source-changed', value);
};

/**
 * 重置来源选择状态
 */
const resetSource = () => {
    selectedSource.value = null;
    emit('source-changed', null);
};

// 监听初始来源变化
watch(
    () => props.initialSource,
    (newValue) => {
        selectedSource.value = newValue;
    }
);

// 向父组件暴露方法
defineExpose({
    resetSource,
    selectSource,
});
</script>

<style scoped>
.source-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    background: transparent;
    border: none;
    gap: 8px;
}

.source-menu-item {
    flex-shrink: 0;
}

/* SelectButton 样式覆盖 */
:deep(.p-selectbutton .p-button) {
    font-size: 0.875rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    /* color: var(--text-color); */
}

:deep(.p-selectbutton .p-button:hover) {
    color: var(--text-color);
}

:deep(.p-selectbutton .p-button.p-highlight) {
    color: var(--primary-color) !important;
}
</style>
