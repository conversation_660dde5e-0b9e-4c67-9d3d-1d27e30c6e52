<template>
    <div class="server-category-list">
        <Loading v-if="loading" text="加载中..." />
        <div v-else-if="error" class="category-error">{{ error }}</div>
        <div v-else-if="categories.length === 0" class="category-empty">
            <i class="pi pi-inbox"></i>
            <span>暂无分类数据</span>
        </div>
        <div v-else>
            <div
                v-for="cat in categories"
                :key="cat.id"
                class="category-item"
                :class="{ 'category-item-active': selectedCategoryId === cat.id }"
                @click="selectCategory(cat)"
            >
                <span class="category-name">{{ cat.name }}</span>
                <span class="category-count">{{ cat.count || 0 }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue';
import Loading from '@/components/common/Loading.vue';

// 定义 props
const props = defineProps({
    categories: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    error: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['category-selected']);
const selectedCategoryId = ref(null);

const selectCategory = cat => {
    console.log('selectCategory', props.categories);
    // 如果已选中，则取消选中
    if (selectedCategoryId.value === cat.id) {
        selectedCategoryId.value = null;
        emit('category-selected', null);
    } else {
        selectedCategoryId.value = cat.id;
        emit('category-selected', cat.id);
    }
};

// 暴露重置方法，以便父组件调用
const resetCategory = () => {
    selectedCategoryId.value = null;
};

// 向父组件暴露重置方法
defineExpose({
    resetCategory
});
</script>

<style scoped lang="scss">
.server-category-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 8px;
    padding: 7px 14px;
    cursor: pointer;
    transition: background 0.15s, box-shadow 0.15s, border 0.15s;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.04);
    border: 1px solid #f0f0f0;
    min-height: 32px;
    margin-bottom: 10px;
}

.category-item:hover {
    background: #f5f7fa;
    border-color: #e0e7ef;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}

/* 选中状态样式 */
.category-item-active {
    background: #eaf2fe;
    border-color: #409eff;
    box-shadow: 0 2px 8px 0 rgba(64, 158, 255, 0.15);
}

.category-item-active:hover {
    background: #eaf2fe;
    border-color: #409eff;
    box-shadow: 0 2px 8px 0 rgba(64, 158, 255, 0.15);

    .category-count {
        background: var(--primary-color);
        color: #fff;
    }
}

.category-name {
    font-weight: 400;
    letter-spacing: 0.1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    font-size: 0.9rem;
}

.category-count {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f6fa;
    color: var(--primary-color);
    font-size: 13px;
    font-weight: 500;
    border-radius: 4px;
    padding: 0 6px;
    height: 22px;
    margin-left: 10px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
    transition: background 0.15s, color 0.15s;
}

.category-item:hover .category-count {
    background: var(--primary-color);
    color: #fff;
}

/* 选中状态下的计数器样式 */
.category-item-active .category-count {
    background: var(--primary-color);
    color: #fff;
}

/* .category-item-active:hover .category-count {
        background: #66b1ff;
        color: #fff;
    } */

.category-loading,
.category-error,
.category-empty {
    color: #999;
    padding: 12px 0;
    text-align: center;
    font-size: 13px;
}

.category-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-color-secondary);
    height: 200px;
}

.category-empty i {
    font-size: 24px;
    color: var(--text-color-secondary);
    opacity: 0.6;
}
</style>
