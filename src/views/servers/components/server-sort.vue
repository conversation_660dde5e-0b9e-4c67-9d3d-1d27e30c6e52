<template>
    <div class="server-sort">
        <Dropdown
            v-model="selectedSort"
            :options="sortOptions"
            option-label="label"
            placeholder="排序方式"
            class="sort-dropdown"
            @change="handleSortChange"
        >
            <template #value="slotProps">
                <div v-if="slotProps.value" class="sort-value">
                    <i :class="getSortIcon(slotProps.value.value)"></i>
                    <span>{{ slotProps.value.label }}</span>
                </div>
                <span v-else>排序方式</span>
            </template>
            <template #option="slotProps">
                <div class="sort-option">
                    <i :class="getSortIcon(slotProps.option.value)"></i>
                    <span>{{ slotProps.option.label }}</span>
                </div>
            </template>
        </Dropdown>
    </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue';

const props = defineProps({
    initialSort: {
        type: [String, Number],
        default: 0,
    },
});

const emit = defineEmits(['sort-changed']);

// 排序选项配置
const sortOptions = [
    { label: '默认排序', value: 0 },
    { label: '发布时间升序', value: 1 },
    { label: '发布时间降序', value: 2 },
    { label: '更新时间升序', value: 3 },
    { label: '更新时间降序', value: 4 },
    { label: 'Git Star数升序', value: 5 },
    { label: 'Git Star数降序', value: 6 },
    { label: '下载数升序', value: 7 },
    { label: '下载数降序', value: 8 },
];

// 选择的排序方式
const selectedSort = ref(
    sortOptions.find((option) => option.value === Number(props.initialSort)) || sortOptions[0]
);

/**
 * 获取排序图标
 * @param {number} value - 排序值
 * @returns {string} 图标类名
 */
const getSortIcon = (value) => {
    if (value === 0) {
        return 'pi pi-sort-alt';
    }
    if ([2, 4, 6, 8].includes(value)) {
        return 'pi pi-sort-amount-down';
    }
    if ([1, 3, 5, 7].includes(value)) {
        return 'pi pi-sort-amount-up-alt';
    }
    return 'pi pi-sort';
};

/**
 * 处理排序变化
 */
const handleSortChange = () => {
    emit('sort-changed', selectedSort.value.value);
};

// 监听初始排序变化
watch(
    () => props.initialSort,
    (newSort) => {
        const sortValue = Number(newSort);
        const foundOption = sortOptions.find((option) => option.value === sortValue);
        selectedSort.value = foundOption || sortOptions[0];
    }
);
</script>

<style scoped>
.server-sort {
    width: 200px;
    height: 32px;
}

.sort-dropdown {
    width: 100%;
    font-size: 14px;
}

.sort-dropdown :deep(.p-dropdown) {
    border-radius: 8px;
    transition: all 0.2s ease;
}

.sort-dropdown :deep(.p-dropdown:hover) {
    border-color: var(--primary-color);
}

.sort-dropdown :deep(.p-dropdown:focus) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-500-rgb), 0.2);
}

.sort-value,
.sort-option {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.sort-value i,
.sort-option i {
    font-size: 12px;
    color: var(--text-color-secondary);
}

.sort-option:hover i {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .server-sort {
        width: 100%;
    }
}
</style>
