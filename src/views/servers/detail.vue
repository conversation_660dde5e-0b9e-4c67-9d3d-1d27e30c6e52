<template>
    <DetailLayout :detail-data="detailData" :tabs="tabs" default-tab="detail">
        <template #header>
            <DetailHeader :detail-data="detailData">
                <template #meta>
                    <div class="meta-item">
                        <i class="pi pi-clock" title="更新时间"></i>
                        <span>{{ formatDate(detailData?.modifiedStime) }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="pi pi-user" title="作者"></i>
                        <span v-if="detailData?.submitter !== 'admin'"
                            >@{{ detailData?.submitter }}</span
                        >
                        <span v-else>{{ detailData?.supplier }}</span>
                    </div>
                    <div class="meta-item" v-if="detailData?.programLanguage.length">
                        <i class="pi pi-globe" title="语言"></i>
                        <span>{{ detailData?.programLanguage }}</span>
                    </div>
                </template>
                <template #actions>
                    <div
                        v-if="detailData?.gitUrl && detailData.gitUrl.length > 0"
                        class="git-repo"
                        @click.stop="openGitUrl(detailData.gitUrl)"
                        title="仓库"
                    >
                        <i v-if="gitRepoType === GitRepoType.GITHUB" class="pi pi-github"></i>
                        <img
                            v-else-if="gitRepoType === GitRepoType.CORP"
                            src="@/assets/img/gitlab.png"
                            alt="GitLab"
                            class="git-icon"
                        />
                    </div>
                </template>
            </DetailHeader>
        </template>

        <template #detail>
            <div v-if="loading" class="loading-container">
                <i class="pi pi-spin pi-spinner"></i>
                <span>加载中...</span>
            </div>
            <div v-else-if="error" class="error-container">
                <i class="pi pi-exclamation-triangle"></i>
                <span>{{ error }}</span>
                <Button label="重试" @click="getDetailData" />
            </div>
            <DetailContent v-else :content="detailData?.introduction || ''" />
        </template>

        <template #config>
            <Card class="detail-content-sub">
                <template #title>
                    <div class="detail-serverConfig">
                        Server Config<i class="pi pi-copy" title="点击复制" @click="copyTxt"></i>
                    </div>
                </template>
                <template #content>
                    <div class="detail-serverConfig-code">
                        <pre ref="codeRef">
                            <code class="language-json">{{ formatServerConfig(detailData.serverConfig) }}</code>
                        </pre>
                    </div>
                </template>
            </Card>
        </template>

        <template #tools>
            <div class="tools-content">
                <p>工具测试功能开发中...</p>
            </div>
        </template>
    </DetailLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useServerStore } from '@/stores/server-store.js';
import DetailLayout from '@/components/detail/detail-layout.vue';
import DetailHeader from '@/components/detail/detail-header.vue';
import DetailContent from '@/components/detail/detail-content.vue';
import { getGitRepoType, GitRepoType, formatDate } from '@/utils/commonUtils';

// markdown渲染相关
import 'github-markdown-css/github-markdown.css';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

// 路由和状态管理
const router = useRouter();
const serverStore = useServerStore();

// 响应式数据
const detailData = ref(null);
const loading = ref(false);
const error = ref('');
const markdownText = ref('');

// 选项卡配置
const tabs = ref([
    { title: '服务详情', key: 'detail' }
    // { title: '工具测试', key: 'tools' }
]);

// Event handlers and utility functions for the config section moved from detail-layout
const codeRef = ref(null);

// 点击拷贝相关
import copy from 'copy-to-clipboard';
// 复制代码并提示
import { useToast } from 'primevue/usetoast';
const toast = useToast();
const copyTxt = () => {
    if (codeRef.value) {
        const codeElement = codeRef.value.querySelector('code');
        if (codeElement) {
            const codeText = codeElement.textContent;
            copy(codeText);
            // 显示成功提示
            toast.add({
                severity: 'success',
                summary: '成功',
                detail: '代码已复制到剪贴板',
                life: 3000
            });
        } else {
            // 显示错误提示
            toast.add({
                severity: 'error',
                summary: '失败',
                detail: '未找到代码元素',
                life: 3000
            });
        }
    }
};

const formatServerConfig = config => {
    if (config === null || config === undefined) {
        return '';
    }
    try {
        // Attempt to parse if it's a string, otherwise assume it's already an object
        const parsedConfig = typeof config === 'string' ? JSON.parse(config) : config;
        return JSON.stringify(parsedConfig, null, 4);
    } catch (e) {
        console.error('Error formatting server config:', e);
        return String(config); // Return original or string representation on error
    }
};

// 计算属性
const gitRepoType = computed(() => {
    return getGitRepoType(detailData.value?.gitUrl);
});

/**
 * 打开Git仓库链接
 */
const openGitUrl = () => {
    if (detailData.value?.gitUrl) {
        window.open(detailData.value.gitUrl, '_blank');
    }
};

/**
 * 获取详情数据
 */
const getDetailData = async () => {
    try {
        loading.value = true;
        error.value = '';

        const route = router.currentRoute.value;
        const id = route.params.id;

        if (!id) {
            throw new Error('URL 中未找到 id 参数');
        }

        const response = await serverStore.getServerDetail(id);

        if (response?.result) {
            // 处理 tags 字段
            // if (typeof response.result.tags === 'string') {
            //     response.result.tags = response.result.tags.split(',').filter(tag => tag.trim());
            // }

            detailData.value = response.result;
            markdownText.value = response.result.introduction || '';

            // 翻译，等待成熟插件开发
            // currentLanguage = translate.language.recognition(markdownText.value).languageName;
            console.log('获取详情数据成功:', detailData.value);
        } else {
            throw new Error('服务器详情数据格式错误');
        }
    } catch (err) {
        console.error('获取详情数据失败:', err);
        error.value = err.message || '获取详情数据失败';
    } finally {
        loading.value = false;
    }
};

// Lifecycle hooks
onMounted(() => {
    getDetailData();
    // Initial highlighting on mount if data is already available
    setTimeout(() => {
        // Use setTimeout to ensure DOM is updated
        if (codeRef.value) {
            const codeElement = codeRef.value.querySelector('code');
            if (codeElement) {
                hljs.highlightElement(codeElement);
            }
        }
    }, 0);
});

// Watch for detailData changes to re-apply highlighting
watch(
    () => detailData.value,
    newData => {
        if (newData) {
            // Need to wait for the DOM to update after data changes
            setTimeout(() => {
                if (codeRef.value) {
                    const codeElement = codeRef.value.querySelector('code');
                    if (codeElement) {
                        hljs.highlightElement(codeElement);
                    }
                }
            }, 0);
        }
    }
);
</script>

<style lang="scss" scoped>
.meta-item {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: var(--text-color-secondary);
    font-size: 14px;

    .pi {
        font-size: 14px;
    }
}

.git-repo {
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
        background-color: var(--surface-hover);
    }

    .git-icon {
        width: 16px;
        height: 16px;
    }
}

.loading-container,
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 1rem;
    color: var(--text-color-secondary);
}

.error-container {
    color: var(--red-500);
}

.tools-content {
    padding: 24px;
    text-align: center;
    color: var(--text-color-secondary);
}

:deep(.p-button) {
    .p-button-icon {
        font-size: 1rem;
    }
}

//右侧config
.detail-content-sub {
    margin-left: 24px;
    width: 336px;
    align-self: flex-start;
    box-shadow: none;
    border-radius: 8px;
    flex-shrink: 0;
    //background: #f7f9fd;
    border: 1px solid var(--surface-200);

    :deep(.p-card-body) {
        padding: 16px;
    }
}

pre code {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    padding: 0;
}

/* 控制 JSON 结构换行 */
.hljs-punctuation,
.hljs-attr,
.hljs-string {
    display: inline-block;
}

/* 为不同层级添加缩进 */
.hljs-punctuation + .hljs-attr,
.hljs-punctuation + .hljs-punctuation {
    margin-left: 2em;
}

.detail-serverConfig {
    display: flex;
    justify-content: space-between;

    .pi-copy {
        font-size: 16px;
        cursor: pointer;
    }

    &-code {
        //border-top: 1px solid #eee;
        pre {
            margin: 0;
            font-size: 0;
        }
    }
}

.detail-con {
    display: flex;

    &-main {
        flex: 1;
    }

    &-sub {
        margin-left: 12px;
        width: 336px;
        align-self: flex-start;
    }
}

/* json高亮 */
.language-json {
    padding: 12px;
    line-height: 24px;
    font-size: 12px;
    background: #f7f9fd;
    border-radius: 8px;
}
</style>
