<template>
    <div class="server-index">
        <div class="index-header">
            <h1 class="index-title">
                寻找超棒的 <span class="highlight">MCP Server</span> and
                <span class="highlight">Client</span>
            </h1>
            <div class="search-container">
                <InputText
                    v-model="keyword"
                    type="text"
                    class="w-full"
                    placeholder="搜索关键词..."
                    @input="handleSearch"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { debounce } from '@/utils/commonUtils';

// 定义要发出的事件
const emit = defineEmits(['search']);

// 搜索关键词
const keyword = ref('');

// 处理搜索，使用防抖优化
const handleSearch = debounce(() => {
    emit('search', keyword.value);
}, 500);
</script>

<style lang="scss">
.server-index {
    width: 100%;
    padding: 60px 0 32px;
}

.index-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.index-badge {
    display: inline-flex;
    align-items: center;
    background: #fff;
    border-radius: 20px;
    padding: 2px;
    border: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.badge-number {
    background-color: $system-blue;
    color: white;
    border-radius: 20px;
    padding: 5px 10px;
    font-weight: 600;
}

.badge-text {
    padding: 5px 10px;
    color: #646464;
    font-weight: 500;
}

.index-title {
    font-size: clamp(1.75rem, 5vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 16px;
    color: #333;
    transition: transform 0.3s ease;
}

.highlight {
    color: var(--primary-color);
    position: relative;

    &::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary-color);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    &:hover::after {
        transform: scaleX(1);
    }
}

.index-sponsor {
    font-size: 1rem;
    color: #646464;
    margin-bottom: 30px;
}

.sponsor-link {
    color: #f5a623;
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

.search-container {
    width: 100%;
    max-width: 800px;
    margin: 24px 0 0;
    transition: all 0.3s ease;

    .p-inputtext {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        font-size: 1rem;

        &:focus {
            box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
        }
    }
}
</style>
