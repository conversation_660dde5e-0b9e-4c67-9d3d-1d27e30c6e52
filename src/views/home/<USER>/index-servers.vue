<template>
    <div class="index-servers">
        <!-- 服务器数据展示 -->
        <div v-if="displayServers.length" class="section-container">
            <div class="section-header">
                <h2 class="section-title">MCP Server</h2>
                <p class="section-more" @click="viewAll('server')">
                    <span>查看全部</span><i class="pi pi-arrow-right"></i>
                </p>
            </div>
            <div class="servers-grid">
                <div
                    v-for="(server, index) in displayServers"
                    :key="`server-${server.id || index}`"
                    class="item-card"
                    :style="{ animationDelay: `${index * 0.05}s` }"
                >
                    <ServerCard :server="server" />
                </div>
            </div>
        </div>

        <!-- 客户端数据展示 -->
        <div v-if="displayClients.length" class="section-container">
            <div class="section-header">
                <h2 class="section-title">MCP Client</h2>
                <p
                    v-if="displayClients.length >= 8"
                    class="section-more"
                    @click="viewAll('client')"
                >
                    <span>查看全部</span><i class="pi pi-arrow-right"></i>
                </p>
            </div>
            <div class="clients-grid">
                <div
                    v-for="(client, index) in displayClients"
                    :key="`client-${client.id || index}`"
                    class="item-card"
                    :style="{ animationDelay: `${index * 0.05}s` }"
                >
                    <ClientCard :client="client" />
                </div>
            </div>
        </div>

        <!-- 用例数据展示 -->
        <div v-if="false" class="section-container">
            <div class="section-header">
                <h2 class="section-title">UseCase</h2>
                <p
                    v-if="displayClients.length >= 8"
                    class="section-more"
                    @click="viewAll('usecase')"
                >
                    <span>查看全部</span><i class="pi pi-arrow-right"></i>
                </p>
            </div>
            <div class="usecases-grid">
                <div
                    v-for="(usecase, index) in displayUsecases"
                    :key="`usecase-${usecase.id || index}`"
                    class="item-card"
                    :style="{ animationDelay: `${index * 0.05}s` }"
                >
                    <UsecaseCard :usecase="usecase" />
                </div>
            </div>
        </div>
        <!-- 无数据展示  -->
        <div class="no-data-container">
            <DataStatus
                v-if="!displayServers.length && !displayClients.length && !displayUsecases.length"
                :is-empty="true"
                :status-text="'暂无数据'"
            />
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useHomeStore } from '@/stores/home-store';
import ServerCard from '@/components/server/server-card.vue';
import ClientCard from '@/components/client/client-card.vue';
import UsecaseCard from '@/components/usecase/usecase-card.vue';
import DataStatus from '@/components/common/data-status.vue';

// 路由器实例
const router = useRouter();
const homeStore = useHomeStore();

// 定义事件
// const emit = defineEmits(['reload']);

// 接收父组件传递的数据
const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            server: { values: [], totalCount: 0 },
            client: { values: [], totalCount: 0 },
            usecase: { values: [], totalCount: 0 },
        }),
    },
    loading: {
        type: Boolean,
        default: false,
    },
    error: {
        type: [String, null],
        default: null,
    },
    source: {
        type: [Number, null],
        default: null,
    },
    keyword: {
        type: String,
        default: '',
    },
});

// 错误状态计算
// const hasError = computed(() => !!props.error);
// const errorMessage = computed(() => props.error || '加载失败，请稍后再试');

// 计算服务器数据
const displayServers = computed(() => {
    return props.data && props.data.server && props.data.server.values
        ? props.data.server.values
        : [];
});

// 计算客户端数据
const displayClients = computed(() => {
    return props.data && props.data.client && props.data.client.values
        ? props.data.client.values
        : [];
});

// 计算用例数据
const displayUsecases = computed(() => {
    return props.data && props.data.usecase && props.data.usecase.values
        ? props.data.usecase.values
        : [];
});

// 查看全部按钮的事件处理
const viewAll = (type) => {
    // 确保关键词被设置到 store 中
    if (props.keyword) {
        homeStore.setKeyword(props.keyword);
    }

    router.push({
        name: type + 'List',
    });
};

onMounted(() => {
    console.log('index-servers组件挂载完成, 数据:', props.data);
});
</script>

<style scoped lang="scss">
.loading-state {
    margin: 60px auto;
}

.error-message {
    margin: 40px 0;
}

.section-container {
    margin-bottom: 40px;
}

.section-more {
    font-size: 0.85rem;
    font-weight: bold !important;
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;

    &:hover {
        color: var(--primary-color, $system-blue);
    }

    i {
        font-size: 12px;
    }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        color: var(--text-color);
    }
}

.servers-grid,
.clients-grid,
.usecases-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.item-card {
    width: calc(25% - 18px);
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
    box-sizing: border-box;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
