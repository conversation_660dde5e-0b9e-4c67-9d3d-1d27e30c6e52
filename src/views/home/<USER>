<template>
    <div class="home-page">
        <div class="home-container">
            <IndexContent @search="handleSearch" />
            <IndexCategory @change-category="handleChangeCategory" />
            <Loading v-if="homeStore.loading" text="数据加载中" />
            <div v-else-if="homeStore.error" class="servers-error-container">
                <Message severity="error" :closable="false">
                    {{ homeStore.error }}
                </Message>
            </div>
            <div v-else class="servers-content-container">
                <IndexServers
                    :data="filteredData"
                    :source="homeStore.source"
                    :keyword="homeStore.keyword"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed, reactive } from 'vue';
import IndexContent from './components/index-content.vue';
import IndexCategory from './components/index-category.vue';
import IndexServers from './components/index-servers.vue';
import Loading from '@/components/common/Loading.vue';
import { useHomeStore } from '@/stores/home-store';

const homeStore = useHomeStore();

// 根据当前分类过滤数据
const filteredData = computed(() => {
    if (!homeStore.homeData) {
        return {
            server: { values: [], totalCount: 0 },
            client: { values: [], totalCount: 0 },
            usecase: { values: [], totalCount: 0 },
        };
    }

    const result = homeStore.homeData;

    return result;
});

// 切换分类的方法
const handleChangeCategory = (source) => {
    console.log('handleChangeCategory', source);
    if (typeof source === 'number') {
        homeStore.setSource(source);
        getGlobalSearch();
    }
};

const searchForm = reactive({
    returnNumPerType: 8,
});

// 处理搜索事件
const handleSearch = (keyword) => {
    console.log('handleSearch', keyword);
    homeStore.setKeyword(keyword);
    getGlobalSearch();
};

const getGlobalSearch = async () => {
    try {
        const params = {
            returnNumPerType: searchForm.returnNumPerType,
            keyword: homeStore.keyword,
        };

        if (homeStore.source > 0) {
            params.source = homeStore.source;
        }

        await homeStore.globalSearch(params);
    } catch (error) {
        console.error('获取数据失败:', error);
    }
};

onMounted(async () => {
    homeStore.clearSource(); // 每次进入首页都重置分类
    await getGlobalSearch();
});
</script>

<style lang="scss">
.home-page {
    width: 100%;
    min-height: 100%;
}

.home-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    min-height: 100%;
}

.servers-error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}
</style>
