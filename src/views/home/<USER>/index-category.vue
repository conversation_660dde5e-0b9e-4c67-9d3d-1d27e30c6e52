<template>
    <div class="category-container">
        <div class="custom-tab-menu">
            <ul class="tab-menu-nav">
                <li
                    v-for="(item, index) in menuItems"
                    :key="index"
                    :class="[
                        'tab-menu-item',
                        activeTabIndex === index ? 'tab-menu-item-active' : '',
                    ]"
                >
                    <a class="tab-menu-link" href="#" @click.prevent="setActiveTab(index)">
                        <i :class="item.icon"></i>
                        <span>{{ item.label }}</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, defineEmits } from 'vue';

const emit = defineEmits(['changeCategory']);

// 当前激活的选项卡索引
const activeTabIndex = ref(0);

// 定义菜单项
const menuItems = computed(() => [
    {
        label: '全部',
        icon: 'pi pi-th-large',
        command: () => setActiveTab(0),
    },
    {
        label: '汽车之家',
        icon: 'pi pi-home',
        command: () => setActiveTab(1),
    },
    {
        label: '官方',
        icon: 'pi pi-sitemap',
        command: () => setActiveTab(2),
    },
    {
        label: '第三方',
        icon: 'pi pi-users',
        command: () => setActiveTab(3),
    },
]);

// 设置激活的选项卡
const setActiveTab = (index) => {
    activeTabIndex.value = index;
    emit('changeCategory', index);
};
</script>

<style lang="scss" scoped>
.category-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.custom-tab-menu {
    .tab-menu-nav {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        background: transparent;
        border: none;
    }

    .tab-menu-link {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 16px;
        margin: 0 6px;
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 0.9rem;
        color: var(--text-color);
        text-decoration: none;

        i {
            margin-right: 0.4rem;
            font-size: 0.9rem;
        }
    }

    .tab-menu-item-active {
        .tab-menu-link {
            background-color: var(--primary-color);
            color: var(--primary-color-text);
        }
    }
}
</style>
