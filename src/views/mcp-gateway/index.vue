<template>
    <div class="mcp-gateway-page">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="title-word">MCP</span>
                        <span class="title-word">网关</span>
                        <span class="title-word accent-text">解决方案</span>
                    </h1>
                    <p class="hero-description">
                        将现有的 <span class="highlight">REST API 项目</span> 或
                        <span class="highlight">接口</span> 通过配置转换为 MCP 服务 —
                        <span class="highlight highlight-green">无需更改一行代码</span>
                    </p>
                    <div class="cta-row">
                        <div class="cta-btn primary" @click="openProductionEnvironment">
                            <span>立即开始</span>
                            <i class="pi pi-arrow-right text-sm"></i>
                        </div>
                        <div class="cta-btn secondary" @click="navigateToMCPGatewayDocs">
                            <span>查看文档</span>
                            <i class="pi pi-arrow-right text-sm"></i>
                        </div>
                    </div>
                    <div class="contact-info">
                        技术支持：<span class="contact-link" @click="handleContactClick"
                            >@周晓明</span
                        >
                        <span class="divider">|</span>
                        <span class="link-text" @click="openIssueLink">问题反馈</span>
                    </div>
                </div>
                <div class="hero-diagram">
                    <WorkflowDiagram />
                </div>
            </div>
        </section>

        <!-- 核心功能区域 - 交互式图文体验 -->
        <section class="features-section">
            <div class="container">
                <h2 class="section-title">核心功能</h2>
                <div class="interactive-features">
                    <div class="features-text">
                        <div class="feature-tabs">
                            <!-- <div class="feature-tabs-tip">点击下方卡片切换核心功能体验</div> -->
                            <div
                                class="feature-tab"
                                v-for="(feature, index) in interactiveFeatures"
                                :key="index"
                                :class="{ active: activeFeatureIndex === index }"
                                @click="setActiveFeature(index)"
                            >
                                <div class="tab-content">
                                    <h3 class="feature-title">
                                        <span>{{ feature.title }}</span>
                                        <i
                                            :class="[
                                                'feature-icon',
                                                feature.icon,
                                                { active: activeFeatureIndex === index }
                                            ]"
                                        ></i>
                                    </h3>
                                    <transition name="expand-fade">
                                        <div
                                            v-if="activeFeatureIndex === index"
                                            class="expand-content"
                                        >
                                            <p class="feature-description">
                                                {{ feature.description }}
                                            </p>
                                            <div class="detail-benefits">
                                                <h4>核心优势</h4>
                                                <ul>
                                                    <li
                                                        v-for="benefit in feature.benefits"
                                                        :key="benefit"
                                                    >
                                                        {{ benefit }}
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="features-images">
                        <!-- Swiper 左右箭头按钮 -->
                        <div class="swiper-button-prev"></div>
                        <Swiper
                            :modules="swiperModules"
                            :slides-per-view="1"
                            :space-between="0"
                            :allow-touch-move="false"
                            class="feature-swiper"
                            :navigation="{
                                nextEl: '.swiper-button-next',
                                prevEl: '.swiper-button-prev'
                            }"
                            @swiper="onSwiperInit"
                            @slideChange="onSlideChange"
                        >
                            <SwiperSlide
                                v-for="(feature, index) in interactiveFeatures"
                                :key="index"
                            >
                                <div class="feature-image-container">
                                    <img
                                        :src="feature.image"
                                        :alt="feature.title"
                                        class="feature-image"
                                    />
                                </div>
                            </SwiperSlide>
                        </Swiper>
                        <div class="swiper-button-next"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 操作流程区域 -->
        <section class="process-section">
            <div class="container">
                <h2 class="section-title">YAPI 项目转换流程</h2>
                <div class="process-subtitle">6步完成到 MCP Server 的转换</div>
                <div class="process-steps">
                    <div class="step-card" v-for="(step, index) in processSteps" :key="index">
                        <div class="step-header">
                            <div class="step-number">{{ String(index + 1).padStart(2, '0') }}</div>
                            <h3>{{ step.title }}</h3>
                        </div>
                        <p>{{ step.description }}</p>
                        <div class="step-actions" v-if="step.actions">
                            <button
                                class="action-btn"
                                v-for="action in step.actions"
                                :key="action.text"
                                @click="handleActionClick(action)"
                            >
                                {{ action.text }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统信息区域 -->
        <section class="system-info-section" ref="systemInfoSection">
            <div class="container">
                <h2 class="section-title">系统地址</h2>
                <div class="system-grid">
                    <div class="system-card">
                        <div class="system-header">
                            <h3>测试环境</h3>
                            <button class="copy-btn" @click="openTestEnvironment">
                                <i class="pi pi-external-link"></i>
                            </button>
                        </div>
                        <div class="system-address" @click="openTestEnvironment">
                            {{ testEnvironment }}
                        </div>
                        <p>用于开发测试，功能完整稳定</p>
                    </div>
                    <div class="system-card">
                        <div class="system-header">
                            <h3>生产环境</h3>
                            <button class="copy-btn" @click="openProductionEnvironment">
                                <i class="pi pi-external-link"></i>
                            </button>
                        </div>
                        <div class="system-address" @click="openProductionEnvironment">
                            {{ productionEnvironment }}
                        </div>
                        <p>正式生产环境，高可用部署</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 其他功能区域 -->
        <section class="additional-features-section">
            <div class="container">
                <h2 class="section-title">更多功能</h2>
                <div class="additional-grid">
                    <div class="additional-card">
                        <h3>🔧 托管 MCP Server</h3>
                        <p>提供 MCP Server 托管服务</p>
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                justify-content: center;
                            "
                        >
                            <button class="feature-btn disabled">暂不推荐</button>
                        </div>
                        <div class="why-tip">
                            不建议使用本 MCP 网关托管。托管 stdio 类服务可能致系统不稳定；托管 sse
                            或 streamableHttp 类服务，仅作代理用，且需重新配置 Tools
                            描述，暂无实际意义。
                        </div>
                    </div>
                    <div class="additional-card">
                        <h3>💬 在线试用</h3>
                        <p>通过自然语言在线试用 MCP Server（实验阶段）</p>
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                justify-content: center;
                            "
                        >
                            <button class="feature-btn disabled">暂不推荐</button>
                        </div>
                        <div class="why-tip">该功能尚处实验阶段，问题较多，不建议使用。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 行动号召区域 -->
        <section class="cta-section">
            <div class="container">
                <h2>准备好开始了吗？</h2>
                <p>立即体验 MCP 网关，将您的 REST API 转换为强大的 MCP Server</p>
                <div class="cta-actions">
                    <button class="primary-btn large" @click="openProductionEnvironment">
                        <span>立即开始</span>
                    </button>
                    <button class="secondary-btn large" @click="navigateToMCPGatewayDocs">
                        <span>查看文档</span>
                    </button>
                </div>
            </div>
        </section>
        <FirstEnvVisitDialog
            v-model="showFirstEnvDialog"
            @confirm="handleFirstEnvConfirm"
            @contact="handleFirstEnvContact"
        />
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper/modules';
import WorkflowDiagram from './components/WorkflowDiagram.vue';
import FirstEnvVisitDialog from './components/FirstEnvVisitDialog.vue';
import { useDocumentNavigation } from '@/composables/useDocumentNavigation';

// 导入 Swiper 样式
import 'swiper/css';
import 'swiper/css/navigation';

// 响应式数据
const systemInfoSection = ref(null);
const testEnvironment = 'http://unla.mulan.corpautohome.com';
const productionEnvironment = 'http://unla.corpautohome.com';

// 首次访问弹窗逻辑
const showFirstEnvDialog = ref(false);
let envToJump = '';

const FIRST_ENV_VISIT_KEY = 'mcp-gateway-first-env-visit';

// 只要弹窗关闭（无论 confirm 还是直接关闭），都写 localStorage
watch(showFirstEnvDialog, (val, oldVal) => {
    if (oldVal && !val && !localStorage.getItem(FIRST_ENV_VISIT_KEY)) {
        localStorage.setItem(FIRST_ENV_VISIT_KEY, '1');
    }
});

const openTestEnvironment = () => {
    if (!localStorage.getItem(FIRST_ENV_VISIT_KEY)) {
        envToJump = testEnvironment;
        showFirstEnvDialog.value = true;
        return;
    }
    window.open(testEnvironment, '_blank');
};

const openProductionEnvironment = () => {
    if (!localStorage.getItem(FIRST_ENV_VISIT_KEY)) {
        envToJump = productionEnvironment;
        showFirstEnvDialog.value = true;
        return;
    }
    window.open(productionEnvironment, '_blank');
};

const handleFirstEnvConfirm = () => {
    localStorage.setItem(FIRST_ENV_VISIT_KEY, '1');
    if (envToJump) {
        window.open(envToJump, '_blank');
    }
    showFirstEnvDialog.value = false;
};

const handleFirstEnvContact = () => {
    window.open('dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=pvxdjjf', '_blank');
};

// why 按钮相关逻辑
const showWhy = ref([false, false]);
const toggleWhy = idx => {
    showWhy.value[idx] = !showWhy.value[idx];
};

// 文档导航工具
const { navigateToDocument } = useDocumentNavigation();

// Swiper 相关
const swiperModules = [Navigation];
const swiperInstance = ref(null);
const activeFeatureIndex = ref(0);

// 交互式功能数据
const interactiveFeatures = [
    {
        icon: 'pi pi-sync',
        title: 'YAPI 项目转换',
        description: '将托管在 YAPI 的 REST API 项目一键转换为 MCP Server',
        tags: ['YAPI', 'OpenAPI', '自动化'],
        benefits: [
            '支持 YAPI 项目直接导入',
            '自动解析 OpenAPI 规范',
            '一键生成 MCP Server 配置',
            '保持原有 API 结构完整性'
        ],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/unla/feature-1-20250721.png',
        imageCaption: 'YAPI 项目无缝集成，自动化转换流程'
    },
    {
        icon: 'pi pi-code',
        title: 'REST API 转换',
        description: '将任意 REST API 快速转换为 MCP Server 格式',
        tags: ['REST API', '快速转换', '灵活配置'],
        benefits: [
            '支持标准 REST API 规范',
            '智能参数映射和验证',
            '自动生成工具描述',
            '灵活的路由配置选项'
        ],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/unla/feature-2.png',
        imageCaption: 'REST API 快速转换，保持高性能和稳定性'
    },
    {
        icon: 'pi pi-globe',
        title: '多协议支持',
        description: '支持 Streamable HTTP 和 SSE 协议，性能稳定可靠',
        tags: ['HTTP', 'SSE', '高性能'],
        benefits: [
            '支持 MCP SSE',
            '支持 MCP Streamable HTTP',
            '高并发处理能力',
            '稳定的长连接管理'
        ],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/unla/feature-3.png',
        imageCaption: '多协议支持，满足不同场景需求'
    }
];

// 操作流程数据
const processSteps = [
    {
        title: '导出 YAPI 项目',
        description: '从 YAPI 项目的"数据管理"模块导出 swaggerjson 文件',
        actions: null
    },
    {
        title: '格式转换',
        description: '使用 swagger-converter 将文件转换为 OpenAPI 3.0 格式',
        actions: [{ text: '打开转换工具', url: 'http://swaggerconverter.corpautohome.com/' }]
    },
    {
        title: '导入 unla 系统',
        description: '在 unla 网关配置模块导入转换后的 JSON 文件',
        actions: [{ text: '打开生产环境', url: 'http://unla.corpautohome.com' }]
    },
    {
        title: '配置调整',
        description: '根据需求调整 MCP Server 配置，包括描述、工具和路由等',
        actions: null
    },
    {
        title: '获取协议地址',
        description: '获取 Streamable HTTP 或 SSE 协议地址，完成配置',
        actions: null
    },
    // 新增第 6 步
    {
        title: '提交到 MCP 商店',
        description: '如您转换的 MCP Server 可公开予其他用户使用，请提交至 MCP Store',
        actions: [{ text: '前往提交', url: 'http://mcpstore.corpautohome.com/submit' }]
    }
];

// 方法定义
const goToSystemInfo = () => {
    systemInfoSection.value?.scrollIntoView({ behavior: 'smooth' });
};

// Swiper 相关方法
const onSwiperInit = swiper => {
    swiperInstance.value = swiper;
};

const onSlideChange = () => {
    if (swiperInstance.value) {
        activeFeatureIndex.value = swiperInstance.value.activeIndex;
    }
};

const setActiveFeature = index => {
    activeFeatureIndex.value = index;
    if (swiperInstance.value) {
        swiperInstance.value.slideTo(index);
    }
};

const navigateToMCPGatewayDocs = () => {
    // 跳转到 MCP Gateway 相关文档
    navigateToDocument('mcp-unla.md', 'MCP 网关使用说明', true);
};

const handleContactClick = () => {
    window.open('dingtalk://dingtalkclient/action/sendmsg?dingtalk_id=pvxdjjf', '_blank');
};

const openIssueLink = () => {
    window.open(
        'https://git.corpautohome.com/dealer-arch-public/issues/mcp-store-issue/issues',
        '_blank'
    );
};

const handleActionClick = action => {
    if (action.url) {
        window.open(action.url, '_blank');
    }
};

const openSubmitPage = () => {
    window.open('http://mcpstore.corpautohome.com/submit', '_blank');
};
</script>

<style lang="scss" scoped>
.mcp-gateway-page {
    max-width: 1280px;
    margin: 0 auto;
    height: 100%;
    overflow-y: scroll;

    &::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    color: #000;
}

// 英雄区域
.hero-section {
    padding: 80px 0 0;
    position: relative;

    .hero-container {
        margin: 0 auto;
        display: flex;
        gap: 60px;
    }

    .hero-content {
        margin-top: 35px;
        position: relative;
        z-index: 1;
    }

    .hero-diagram {
        position: relative;
        z-index: 1;
        flex: 1;
    }

    .hero-title {
        font-size: 56px;
        font-weight: 700;
        line-height: 1.2;
        margin-top: 0;
        margin-bottom: 24px;
        color: #000;
        text-align: left;

        .title-word {
            display: inline-block;
            margin-right: 0.5rem;
        }

        .accent-text {
            color: #0033ff;
        }
    }

    .hero-description {
        font-size: 16px;
        line-height: 1.7;
        color: #666;
        margin-bottom: 50px;
    }

    .cta-row {
        display: flex;
        gap: 26px;
        justify-content: flex-start;
        margin-bottom: 32px;
        flex-wrap: wrap;
    }

    .cta-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 18px;
        border-radius: 5px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;

        &.primary {
            background: #0033ff;
            color: #fff;
        }

        &.secondary {
            background: #fff;
            color: #0033ff;
            border: 2px solid #0033ff;
        }
    }

    .contact-info {
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 12px;
        flex-wrap: wrap;

        .contact-link,
        .link-text {
            color: #0033ff;
            cursor: pointer;
            text-decoration: none;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }

        .divider {
            color: #cbd5e1;
        }
    }
}

// 功能区域 - 交互式图文体验
.features-section {
    padding: 80px 0;

    .container {
        margin: 0 auto;
    }

    .section-title {
        text-align: center;
        font-size: 40px;
        font-weight: 700;
        margin-bottom: 60px;
        background: linear-gradient(135deg, #000 0%, #0033ff 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .interactive-features {
        display: flex;
        align-items: flex-start;
        gap: 48px;
    }

    .features-text {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 360px;
        min-width: 300px;
        margin-right: 0;
        gap: 0;
    }

    .feature-tabs {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .feature-tabs-tip {
        font-size: 13px;
        color: #888;
        margin-bottom: 12px;
        text-align: left;
        padding-left: 4px;
    }
    .feature-icon {
        margin-right: 8px;
        font-size: 20px;
        color: #bbb;
        transition: color 0.2s;
        vertical-align: middle;
    }
    .feature-tab.active .feature-icon {
        color: #0033ff;
    }
    .feature-tab.active {
        background: #f0f6ff;
        border-color: #0033ff;
        box-shadow: 0 8px 24px rgba(0, 51, 255, 0.12);
    }
    .feature-tab:hover {
        border-color: #0033ff;
    }

    .feature-tab {
        background: #fff;
        border-radius: 14px;
        box-shadow: 0 2px 12px rgba(0, 51, 255, 0.04);
        border: 2px solid #f1f5f9;
        cursor: pointer;
        transition: border-color 0.2s, box-shadow 0.2s, transform 0.2s;
        padding: 18px 20px 18px 20px;
        margin-bottom: 0;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;

        &:hover {
            border-color: #0033ff;
            box-shadow: 0 4px 16px rgba(0, 51, 255, 0.08);
        }

        &.active {
            border-color: #0033ff;
            box-shadow: 0 8px 24px rgba(0, 51, 255, 0.12);
        }

        .tab-content {
            .feature-title {
                margin-top: 0;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 0;
                color: #000;
                display: flex;
                justify-content: space-between;
            }

            .expand-content {
                margin-top: 10px;

                .feature-description {
                    font-size: 14px;
                    color: #666;
                    line-height: 1.5;
                }
            }

            .detail-benefits {
                h4 {
                    font-size: 14px;
                    font-weight: 600;
                    margin-bottom: 10px;
                    color: #0033ff;
                }

                ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    li {
                        position: relative;
                        padding-left: 22px;
                        margin-bottom: 8px;
                        color: #666;
                        line-height: 1.5;
                        font-size: 12px;

                        &::before {
                            content: '✓';
                            position: absolute;
                            left: 0;
                            color: #0033ff;
                            font-weight: bold;
                        }
                    }
                }
            }
        }
    }

    .features-images {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 0;
        height: 100%;
        max-height: 450px; // 限制最高 450px
        overflow: hidden;
        position: relative;

        .swiper-button-prev,
        .swiper-button-next {
            color: #0033ff;
            font-size: 14px !important;
            cursor: pointer;
            transition: opacity 0.3s ease;
            z-index: 10;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: #fff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .swiper-button-prev:after,
        .swiper-button-next:after {
            font-size: 16px;
            font-weight: bold;
        }

        .swiper-button-prev {
            left: 60px;
        }

        .swiper-button-next {
            right: 60px;
        }

        .feature-swiper {
            width: 100%;
            height: 100%;
            max-height: 450px; // 限制最高 450px
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feature-image-container {
            width: 100%;
            height: 100%;
            min-height: 0;
            min-width: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-image {
            max-width: 100%;
            max-height: 450px;
            width: auto;
            height: auto;
            object-fit: contain; // 保证图片完整显示
            display: block;
            margin: 0 auto;
            border: 2px solid #999;
            border-radius: 10px;
            box-sizing: border-box;
        }
    }
}

// 流程区域
.process-section {
    padding: 80px 0 0;

    .container {
        margin: 0 auto;
    }

    .section-title {
        text-align: center;
        font-size: 40px;
        font-weight: 700;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #000 0%, #0033ff 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .process-subtitle {
        text-align: center;
        font-size: 16px;
        color: #666;
        margin-bottom: 60px;
    }

    .process-steps {
        display: grid;
        grid-template-columns: repeat(6, 1fr); // 由5改为6
        gap: 14px; // gap缩小
        position: relative;
    }

    .step-card {
        display: flex;
        flex-direction: column;
        background: #fff;
        border-radius: 12px;
        padding: 16px 8px; // 缩小内边距
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid #f1f5f9;
        position: relative;
        z-index: 1;

        &:hover {
            box-shadow: 0 8px 24px rgba(0, 51, 255, 0.12);
        }

        &:not(:last-child)::after {
            content: '→';
            position: absolute;
            top: 50%;
            right: -10px; // 缩小箭头间距
            transform: translateY(-50%);
            color: #0033ff;
            font-size: 1.2rem; // 缩小箭头
            font-weight: bold;
            z-index: 2;
        }

        .step-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px; // 缩小间距
            margin-bottom: 10px;
            text-align: center;

            .step-number {
                background: linear-gradient(135deg, #0033ff 0%, #7c3aed 100%);
                color: #fff;
                width: 36px; // 缩小圆圈
                height: 36px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 15px; // 缩小字号
                flex-shrink: 0;
                box-shadow: 0 4px 12px rgba(0, 51, 255, 0.3);
            }

            h3 {
                font-size: 15px; // 缩小标题字号
                font-weight: 600;
                color: #000;
                margin: 0;
                line-height: 1.3;
            }
        }

        p {
            color: #666;
            line-height: 1.5;
            margin-top: 0;
            margin-bottom: 10px; // 缩小间距
            font-size: 12px; // 缩小字体
            text-align: center;
        }

        .step-actions {
            display: flex;
            gap: 6px; // 缩小按钮间距
            flex-wrap: wrap;
            justify-content: center;
            margin-top: auto;

            .action-btn {
                background: #f8fafc;
                color: #0033ff;
                border: 1px solid #e2e8f0;
                padding: 2px 6px; // 缩小按钮
                border-radius: 4px;
                font-size: 11px; // 缩小字体
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    background: #0033ff;
                    color: #fff;
                    border-color: #0033ff;
                }
            }
        }
    }

    // 响应式（可选）
    @media (max-width: 1200px) {
        .process-steps {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    @media (max-width: 700px) {
        .process-steps {
            grid-template-columns: repeat(2, 1fr);
        }
    }
}

// 系统信息区域
.system-info-section {
    padding: 80px 0 0;

    .container {
        margin: 0 auto;
    }

    .section-title {
        text-align: center;
        font-size: 30px;
        font-weight: 600;
        margin-bottom: 32px;
        color: #000;
    }

    .system-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .system-card {
        background: #fff;
        border-radius: 8px;
        padding: 20px 16px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;

        .system-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;

            h3 {
                font-size: 18px;
                font-weight: 600;
                color: #000;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .copy-btn {
                background: #0033ff;
                color: #fff;
                border: none;
                width: 28px;
                height: 28px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.8rem;
            }
        }

        .system-address {
            background: #f8fafc;
            padding: 12px;
            border-radius: 6px;
            font-size: 0.85rem;
            color: #0033ff;
            margin-bottom: 12px;
            border: 1px solid #e2e8f0;
            word-break: break-all;
            cursor: pointer;
        }

        p {
            color: #666;
            margin: 0;
            line-height: 1.4;
            font-size: 12px;
        }
    }
}

// 其他功能区域
.additional-features-section {
    padding: 80px 0 0;

    .container {
        margin: 0 auto;
    }

    .section-title {
        text-align: center;
        font-size: 30px;
        font-weight: 600;
        margin-bottom: 32px;
        color: #000;
    }

    .additional-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .additional-card {
        background: #f8fafc;
        border-radius: 8px;
        padding: 20px 16px 10px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;
        border: 1px solid #e2e8f0;

        &:hover {
            box-shadow: 0 4px 16px rgba(0, 51, 255, 0.1);
            background: #fff;
        }

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #000;
        }

        p {
            color: #666;
            line-height: 1.4;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .feature-btn {
            background: #0033ff;
            color: #fff;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;

            &:hover {
                background: #0029cc;
            }

            &.disabled {
                background: #e2e8f0;
                color: #94a3b8;
                cursor: not-allowed;

                &:hover {
                    transform: none;
                }
            }
        }
        .why-tip {
            margin-top: 10px;
            font-size: 12px;
            color: #888;
            text-align: left;
            min-height: 18px;
            letter-spacing: 0.3px;
            line-height: 1.5;
        }
    }
}

// CTA 区域
.cta-section {
    padding: 80px 0 80px;
    text-align: center;

    .container {
        margin: 0 auto;
    }

    h2 {
        font-size: 40px;
        font-weight: 700;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #000 0%, #0033ff 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    p {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
        line-height: 1.6;
    }

    .cta-actions {
        display: flex;
        gap: 16px;
        justify-content: center;
        flex-wrap: wrap;

        .primary-btn,
        .secondary-btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent
                );
                transition: left 0.6s ease;
            }

            &:hover::before {
                left: 100%;
            }

            &.large {
                font-size: 14px;
            }
        }

        .primary-btn {
            background: #0033ff;
            color: #fff;
            box-shadow: 0 4px 8px rgba(0, 51, 255, 0.3);

            &:hover {
                background: #0029cc;
                box-shadow: 0 8px 24px rgba(0, 51, 255, 0.4);
            }
        }

        .secondary-btn {
            background: #fff;
            color: #0033ff;
            border: 1px solid #0033ff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

            &:hover {
                background: #f8fafc;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            }
        }
    }
}

// 展开/收起动画
.expand-fade-enter-active,
.expand-fade-leave-active {
    transition: opacity 0.22s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.22s cubic-bezier(0.4, 0, 0.2, 1);
}

.expand-fade-enter-from,
.expand-fade-leave-to {
    opacity: 0;
    transform: translateY(12px);
}

.expand-fade-enter-to,
.expand-fade-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.highlight {
    color: #0033ff;
    font-weight: 600;
}

.highlight-green {
    color: $system-green;
    font-weight: 400;
}
</style>
