<template>
    <div class="workflow-diagram" ref="diagramContainer">
        <!-- 节点 -->
        <div class="node source-node" ref="apiProject" id="api-project">
            <div class="title">REST API 项目</div>
            <div class="subtitle">符合 OpenAPI 规范</div>
        </div>

        <div class="node source-node" ref="apiInterface" id="api-interface">
            <div class="title">REST API 接口</div>
            <div class="subtitle"></div>
        </div>

        <div class="node" ref="gateway" id="gateway">
            <div class="title">Unla</div>
            <div class="subtitle">MCP 网关</div>
        </div>

        <div class="node" ref="server" id="server">
            <div class="title">MCP Server</div>
            <div class="subtitle">Streamable Http | SSE</div>
        </div>

        <!-- SVG 连接线 -->
        <svg class="connector-svg">
            <path ref="path1" class="connector-path" d=""></path>
            <path ref="path2" class="connector-path" d=""></path>
            <path ref="path3" class="connector-path" d=""></path>
        </svg>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

// 模板引用
const diagramContainer = ref(null);
const apiProject = ref(null);
const apiInterface = ref(null);
const gateway = ref(null);
const server = ref(null);
const troubleshootingText = ref(null);
const path1 = ref(null);
const path2 = ref(null);
const path3 = ref(null);

let animationInterval = null;

// 动态绘制连接线
const drawPaths = () => {
    if (!diagramContainer.value) {
        return;
    }

    const getCoords = (node, fromSide = 'center', toSide = 'center') => {
        const rect = node.getBoundingClientRect();
        const containerRect = diagramContainer.value.getBoundingClientRect();
        const x = rect.left - containerRect.left;
        const y = rect.top - containerRect.top;
        let outX = 0;
        let outY = 0;

        if (fromSide === 'right') {
            outX = x + rect.width;
        } else if (fromSide === 'left') {
            outX = x;
        } else {
            outX = x + rect.width / 2;
        }

        if (toSide === 'top') {
            outY = y;
        } else if (toSide === 'bottom') {
            outY = y + rect.height;
        } else {
            outY = y + rect.height / 2;
        }

        return { x: outX, y: outY };
    };

    const p1_start = getCoords(apiProject.value, 'right');
    const p1_end = getCoords(gateway.value, 'left');
    path1.value.setAttribute('d', `M ${p1_start.x} ${p1_start.y} L ${p1_end.x} ${p1_end.y}`);

    const p2_start = getCoords(apiInterface.value, 'right');
    const p2_end = getCoords(gateway.value, 'left');
    path2.value.setAttribute('d', `M ${p2_start.x} ${p2_start.y} L ${p2_end.x} ${p2_end.y}`);

    const p3_start = getCoords(gateway.value, 'right');
    const p3_end = getCoords(server.value, 'left');
    path3.value.setAttribute('d', `M ${p3_start.x} ${p3_start.y} L ${p3_end.x} ${p3_end.y}`);
};

// 创建数据包
const createPacket = (color, symbol = '') => {
    const packet = document.createElement('div');
    packet.className = 'packet';
    packet.style.backgroundColor = color;
    packet.style.boxShadow = `0 0 12px ${color}`;
    if (symbol) {
        packet.innerHTML = symbol;
    }
    diagramContainer.value.appendChild(packet);
    return packet;
};

// 数据包沿路径动画
const animatePacketAlongPath = (packet, path, duration, onComplete) => {
    let startTime = null;
    const pathLength = path.getTotalLength();

    function animationStep(timestamp) {
        if (!startTime) {
            startTime = timestamp;
        }
        const progress = (timestamp - startTime) / duration;

        if (progress < 1) {
            const point = path.getPointAtLength(progress * pathLength);
            packet.style.left = `${point.x - packet.offsetWidth / 2}px`;
            packet.style.top = `${point.y - packet.offsetHeight / 2}px`;

            // 检查是否经过模糊文字区域
            if (path === path3.value && troubleshootingText.value) {
                const textRect = troubleshootingText.value.getBoundingClientRect();
                const containerRect = diagramContainer.value.getBoundingClientRect();
                const textY = textRect.top - containerRect.top;
                if (point.y > textY && point.y < textY + textRect.height) {
                    troubleshootingText.value.classList.add('highlight');
                } else {
                    troubleshootingText.value.classList.remove('highlight');
                }
            }

            requestAnimationFrame(animationStep);
        } else {
            packet.style.opacity = '0';
            setTimeout(() => packet.remove(), 300);
            if (troubleshootingText.value) {
                troubleshootingText.value.classList.remove('highlight');
            }
            if (onComplete) {
                onComplete();
            }
        }
    }
    requestAnimationFrame(animationStep);
};

// 开始流程动画
const startFlow = () => {
    // Flow 1: API Project -> Gateway
    const packet1 = createPacket('#3498db', '{;}');
    animatePacketAlongPath(packet1, path1.value, 2500, () => {
        gateway.value.classList.add('flash');
        setTimeout(() => gateway.value.classList.remove('flash'), 700);
    });

    // Flow 2: API Interface -> Gateway
    setTimeout(() => {
        const packet2 = createPacket('#2ecc71', '/>');
        animatePacketAlongPath(packet2, path2.value, 2500);
    }, 500);

    // Flow 3: Gateway -> Server
    setTimeout(() => {
        const mergedPacket = createPacket('#9b59b6', '✓');
        animatePacketAlongPath(mergedPacket, path3.value, 2000, () => {
            server.value.classList.add('success');
            setTimeout(() => server.value.classList.remove('success'), 1000);
        });
    }, 3000);
};

// 初始化
const init = () => {
    drawPaths();
    startFlow();
    animationInterval = setInterval(startFlow, 6000);
};

onMounted(() => {
    setTimeout(init, 100); // 确保DOM完全渲染
    window.addEventListener('resize', drawPaths);
});

onBeforeUnmount(() => {
    if (animationInterval) {
        clearInterval(animationInterval);
    }
    window.removeEventListener('resize', drawPaths);
});
</script>

<style lang="scss" scoped>
.workflow-diagram {
    position: relative;
    width: 100%;
    max-width: 700px;
    height: 400px;
    overflow: hidden;
}

/* 节点样式 */
.node {
    position: absolute;
    background-color: #f9fafb; // 微微加深背景色
    border: 2.5px solid #999; // 边框加深
    border-radius: 12px;
    padding: 15px 20px;
    text-align: center;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.13); // 阴影更明显
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .title {
        font-weight: 700;
        font-size: 15px;
        color: #000; // 标题更深
        line-height: 1.2;
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px #fff, 0 0 2px #bbb;
    }

    .subtitle {
        font-size: 12px;
        color: #666; // 子标题更深
        text-shadow: 0 1px 2px #fff;
    }
}

/* 节点位置 */
#api-project {
    top: 10%;
    left: 5%;
}

#api-interface {
    top: 60%;
    left: 5%;
}

#gateway {
    top: 35%;
    left: 50%;
    transform: translateX(-50%);
}

#server {
    top: 35%; // 与 #gateway 完全一致，保证垂直对齐
    right: 5%;
}

/* 呼吸动画效果 */
.source-node {
    animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
    0%,
    100% {
        border-color: #999;
    }
    50% {
        border-color: #0033ff; // 更饱和蓝色
    }
}

/* 闪烁动画 */
.flash {
    animation: flash-animation 0.7s ease;
}

@keyframes flash-animation {
    0% {
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.13);
    }
    50% {
        box-shadow: 0 0 32px 14px rgba(25, 118, 210, 0.55); // 更亮蓝色
    }
    100% {
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.13);
    }
}

/* 成功动画 */
.success {
    animation: success-animation 1s ease;
}

@keyframes success-animation {
    0% {
        border-color: #999;
    }
    50% {
        border-color: #27ae60; // 更饱和绿色
        box-shadow: 0 0 32px 14px rgba(39, 174, 96, 0.55);
    }
    100% {
        border-color: #999;
    }
}

/* SVG 路径样式 */
.connector-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.connector-path {
    fill: none;
    stroke: #999; // 更浅的线条颜色
    stroke-width: 2px; // 线条更细
    stroke-dasharray: 4 2;
    stroke-dashoffset: 0;
    animation: dash-flow 1s linear infinite;
    transition: stroke 0.2s;
}

@keyframes dash-flow {
    to {
        stroke-dashoffset: -12;
    }
}

.connector-path:hover {
    stroke: #0033ff; // hover 高亮
}

/* 数据包样式 */
:deep(.packet) {
    position: absolute;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    z-index: 10;
    transition: opacity 0.3s, box-shadow 0.2s;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid #fff;
    box-shadow: 0 0 10px 2px #000;
    filter: brightness(1.1) contrast(1.2);
}
</style>
