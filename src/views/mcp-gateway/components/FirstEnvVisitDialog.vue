<template>
    <Dialog
        v-model:visible="visible"
        modal
        closable
        showHeader
        class="first-env-visit-dialog"
        :style="{ width: '400px', borderRadius: '16px' }"
        :pt="{
            header: 'first-env-dialog-header'
        }"
    >
        <template #header>
            <div class="dialog-title">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/img/unla/dialog-know.png"
                    alt=""
                    srcset=""
                />
                <span>使用前提示</span>
            </div>
        </template>
        <template #default>
            <div class="dialog-content">
                <div class="dialog-message">
                    使用账号请向
                    <span class="contact-link" @click="onContact">@周晓明</span> 申请
                </div>
            </div>
        </template>
        <template #footer>
            <Button class="p-button-text main-btn" size="small" text @click="onConfirm"
                >已有账号，直接跳转</Button
            >
            <Button class="p-button-secondary contact-btn" size="small" @click="onContact"
                >联系 @周晓明</Button
            >
        </template>
    </Dialog>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
});
const emit = defineEmits(['update:modelValue', 'confirm', 'contact']);

const visible = ref(props.modelValue);

watch(
    () => props.modelValue,
    val => {
        visible.value = val;
    }
);
watch(visible, val => {
    emit('update:modelValue', val);
});

const onConfirm = () => {
    emit('confirm');
    visible.value = false;
};
const onContact = () => {
    emit('contact');
};
</script>

<style lang="scss" scoped>
.first-env-visit-dialog {
    .dialog-header {
        padding: 0;
    }
    .dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .dialog-title {
        font-size: 14px;
        color: #d81e06;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;

        img {
            width: 20px;
            height: 20px;
        }
    }
    .dialog-message {
        font-size: 14px;
        font-weight: 500;
        color: #000;
        text-align: center;
        line-height: 1.7;
        .contact-link {
            color: #0033ff;
            cursor: pointer;
        }
    }
    .dialog-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        width: 100%;
    }
    .main-btn {
        color: #fff;
        background: #0033ff;
        font-size: 12px;
    }
    .contact-btn {
        color: #0033ff;
        background: #fff;
        border: 1px solid #0033ff;
        font-size: 12px;
    }
}
</style>
<style>
.first-env-dialog-header {
    padding: 14px 20px 18px;
}
</style>
