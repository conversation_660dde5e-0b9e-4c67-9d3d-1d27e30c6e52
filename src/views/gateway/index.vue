<template>
    <div class="flex flex-col items-center justify-center h-full">
        <data-status
            :isLoading="false"
            :isEmpty="false"
            :isFailed="false"
            :isNetwork="true"
            :statusTitleVisible="true"
            statusTitle="正在开发中"
            statusText="该功能正在开发中，敬请期待！"
            :hasBtn="false"
        />
    </div>
</template>

<script setup>
import DataStatus from '@/components/common/data-status.vue';
</script>
