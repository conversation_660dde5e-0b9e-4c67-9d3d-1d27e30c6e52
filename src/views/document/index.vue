<template>
    <div class="doc-home">
        <div v-if="loading" class="position-absolute top-0 left-0 w-full h-full">
            <Loading />
        </div>

        <iframe
            ref="iframeRef"
            id="docs-iframe"
            src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/docs/docsify/docs/0724/guide/index.html"
            frameborder="0"
            width="100%"
            height="100%"
            @load="handleIframeLoad"
        ></iframe>
    </div>
</template>

<script setup>
import Loading from '@/components/common/Loading.vue';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';

// 路由信息
const route = useRoute();

// 响应式状态
const loading = ref(true);
const iframeRef = ref(null);

/**
 * 处理iframe加载完成事件
 */
const handleIframeLoad = () => {
    loading.value = false;

    // 检查是否有路由参数需要跳转
    const { page, section } = route.query;
    if (page && section) {
        // 延迟一下确保iframe完全加载
        setTimeout(() => {
            navigateToSection(page, section);
        }, 500);
    } else {
        // 发送初始消息
        sendMessageToIframe({
            type: 'init',
            data: {
                timestamp: Date.now()
            }
        });
    }
};

/**
 * 向iframe发送消息
 * @param {Object} message - 要发送的消息对象
 */
const sendMessageToIframe = message => {
    if (!iframeRef.value?.contentWindow) {
        console.error('iframe未加载完成或不可用');
        return;
    }

    try {
        // 发送消息到iframe
        iframeRef.value.contentWindow.postMessage(message, '*');
    } catch (error) {
        console.error('发送消息到iframe失败:', error);
    }
};

/**
 * 处理来自iframe的消息
 * @param {MessageEvent} event - 消息事件
 */
const handleMessageFromIframe = event => {
    // 验证消息来源（可选，但建议添加）
    if (event.origin !== 'https://z.autoimg.cn') {
        console.warn('收到来自未知域名的消息:', event.origin);
        return;
    }

    try {
        const { type, data } = event.data;
        // 根据消息类型处理
        switch (type) {
            case 'ready':
                console.log('iframe准备就绪');
                break;
            case 'navigation':
                console.log('iframe导航事件:', data);
                break;
            case 'error':
                console.error('iframe报告错误:', data);
                break;
            default:
                console.log('未知消息类型:', type);
        }
    } catch (error) {
        console.error('处理iframe消息失败:', error);
    }
};

/**
 * 导航到指定页面和章节
 * @param {string} page - 页面路径
 * @param {string} section - 章节名称
 */
const navigateToSection = (page, section) => {
    sendMessageToIframe({
        type: 'navigate',
        page,
        section
    });
};

// 监听路由参数变化
watch(
    () => route.query,
    newQuery => {
        const { page, section } = newQuery;
        if (page && section && !loading.value) {
            // 如果iframe已加载完成且路由参数变化，重新导航
            navigateToSection(page, section);
        }
    },
    { immediate: false }
);

// 暴露方法给父组件使用
defineExpose({
    sendMessageToIframe,
    navigateToSection
});

// 生命周期钩子
onMounted(() => {
    // 监听来自iframe的消息
    window.addEventListener('message', handleMessageFromIframe);
});

onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('message', handleMessageFromIframe);
});
</script>

<style lang="scss" scoped>
.doc-home {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

iframe {
    flex: 1;
    min-height: 0;
}
</style>
