<template>
    <DetailLayout :detail-data="detailData" :tabs="tabs" default-tab="detail">
        <template #header>
            <DetailHeader :detail-data="detailData">
                <template #actions>
                    <a class="git-url" :href="detailData?.url" target="_blank">
                        <i class="pi pi-sitemap mr-1" style="vertical-align: middle"></i>
                        <span class="text-sm">官方地址</span>
                    </a>
                </template>

                <template #meta>
                    <div class="meta-item">
                        <i class="pi pi-clock" title="更新时间"></i>
                        <span>{{ formatDate(detailData?.modifiedStime) }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="pi pi-user" title="作者"></i>
                        <span>{{ detailData?.supplier }}</span>
                    </div>
                    <div class="meta-item" v-if="detailData?.programLanguage">
                        <i class="pi pi-globe" title="语言"></i>
                        <span>{{ detailData?.programLanguage }}</span>
                    </div>

                    <div v-if="detailData?.supportOses.length" class="platform-icons">
                        <span
                            v-for="platform in parsePlatformList(detailData?.supportOses)"
                            :key="platform"
                            class="platform-icon"
                        >
                            <img
                                :src="getPlatformIcon(platform)"
                                :alt="platform"
                                :title="platform"
                            />
                        </span>
                    </div>
                    <div class="meta-item">
                        <i class="pi pi-dollar" title="价格"></i>
                        <span>{{ detailData?.priceModel }}</span>
                    </div>
                </template>
            </DetailHeader>
        </template>

        <template #detail>
            <DetailContent :content="detailData?.introduction || ''" />
        </template>

        <template #tools>
            <div class="tools-content">
                <p>工具测试功能开发中...</p>
            </div>
        </template>
    </DetailLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useClientStore } from '@/stores/client-store';
import DetailLayout from '@/components/detail/detail-layout.vue';
import DetailHeader from '@/components/detail/detail-header.vue';
import DetailContent from '@/components/detail/detail-content.vue';
import { getPlatformIcon, parsePlatformList } from '@/enums/oses';
import { formatDate } from '@/utils/commonUtils';
const router = useRouter();
const clientStore = useClientStore();

const detailData = ref(null);

const tabs = [{ title: '客户端详情', key: 'detail' }];

const getDetailData = async () => {
    const id = router.currentRoute.value.params.id;
    if (!id) {
        return;
    }
    const response = await clientStore.getClientDetail(id);
    // 兼容API返回格式
    if (response && response.result) {
        detailData.value = response.result;
    } else if (response) {
        detailData.value = response;
    }
};

onMounted(getDetailData);
</script>

<style lang="scss" scoped>
.meta-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: var(--text-color-secondary);
    font-size: 14px;
    line-height: 1.5;

    .pi {
        font-size: 14px;
        vertical-align: middle;
    }
}

.git-url {
    color: inherit;
    text-decoration: none;
}

.tools-content {
    padding: 24px;
    text-align: center;
    color: var(--text-color-secondary);
}
</style>
