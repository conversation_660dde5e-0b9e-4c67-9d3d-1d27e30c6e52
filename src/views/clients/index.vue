<template>
    <div class="clients-page">
        <div class="clients-content">
            <!-- <h3 class="text-4xl font-bold text-center pt-1 mt-1 mb-1"
                style="color: var(--primary-color);">MCP Clients
            </h3>
            <h4 class="text-lg text-center">MCP clients are the applications that
                connect to <router-link to="/servers" class="underline-link">MCP servers</router-link> .</h4> -->

            <Loading v-if="loading && clientsList.length === 0" text="正在加载MCP Client列表..." />
            <div v-else-if="error" class="state-container error-state">
                <i class="pi pi-exclamation-triangle"></i>
                <p>加载失败: {{ error }}</p>
                <Button
                    label="重试"
                    icon="pi pi-refresh"
                    severity="danger"
                    outlined
                    @click="loadClients"
                />
            </div>
            <!-- 无数据 -->
            <div v-else-if="clientsList.length === 0" class="no-data-container">
                <DataStatus
                    :is-empty="true"
                    :status-text="'请尝试清除筛选或重新搜索'"
                    :status-title-visible="true"
                    :status-title="'暂无符合条件的MCP Client'"
                />
            </div>

            <!-- 有数据 -->
            <div v-else class="client-grid">
                <div v-for="client in clientsList" :key="client.id" class="client-item">
                    <ClientCard :client="client" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import ClientCard from '@/components/client/client-card.vue';
import { useClientStore } from '@/stores/client-store';

const clientStore = useClientStore();
const clientsList = computed(() => clientStore.clientsList);
const loading = computed(() => clientStore.loading);
const error = computed(() => clientStore.error);
const currentPage = ref(1);
const pageSize = ref(30);
// const totalRecords = ref(0);
onMounted(() => {
    loadClients();
});

const loadClients = async () => {
    loading.value = true;
    await clientStore.getClients({
        pageIndex: currentPage.value,
        pageSize: pageSize.value,
    });
    loading.value = false;
    console.log('客户端列表:', clientsList.value);
};
</script>
<style lang="scss" scoped>
.clients-page {
    color: #1f2937;
    padding-top: 2.5rem;

    .client-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        width: 100%;
        padding-top: 16px;
    }

    .client-item {
        width: calc(100% / 4 - 12px);
        flex-shrink: 0;
    }
}

.underline-link {
    text-decoration: underline;
    color: inherit;

    &:hover {
        color: var(--primary-color);
    }
}
</style>
