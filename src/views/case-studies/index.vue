<template>
    <main class="mcp-usecases-main">
        <section class="mcp-usecases-section">
            <div class="mcp-usecases-container">
                <div class="mcp-usecases-header">
                    <h1 class="mcp-usecases-title">MCP Server Use Cases</h1>
                    <p class="mcp-usecases-desc">How to use MCP Servers with creative ways.</p>
                </div>

                <div class="mcp-usecases-cards">
                    <!-- Milvus Card -->
                    <div class="mcp-usecase-card">
                        <div class="mcp-usecase-card-header">
                            <img
                                src="https://x.autoimg.cn/dealer/dealer_front/public/public_img/0.0.0/dlr_white_main2.png"
                                alt="Milvus"
                                class="avatar"
                            />
                            <div>
                                <div class="user-row">
                                    <span class="user-name">Milvus</span>
                                </div>
                                <div class="user-id">@milvusio</div>
                            </div>
                        </div>
                        <p class="mcp-usecase-card-desc">
                            Connect your Milvus Database via MCP Server to Claude or Cursor. It
                            helps you interact with your Milvus Database through:
                        </p>

                        <div class="mcp-usecase-video">
                            <video class="video" controls>
                                <source
                                    src="https://video.twimg.com/tweet_video/GmBHofcXEAI1vvg.mp4"
                                    type="video/mp4"
                                />
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>

                    <!-- Yarik Card -->
                    <div class="mcp-usecase-card">
                        <div class="mcp-usecase-card-header">
                            <img
                                src="https://x.autoimg.cn/dealer/dealer_front/public/public_img/0.0.0/dlr_white_main2.png"
                                alt="Yarik"
                                class="avatar"
                            />
                            <div>
                                <div class="user-row">
                                    <span class="user-name">Yarik</span>
                                </div>
                                <div class="user-id">@yarik_ai</div>
                            </div>
                        </div>
                        <p class="mcp-usecase-card-desc">
                            I've just built custom MCP server for Cursor. And launched it locally.
                            This MCP makes Cursor play sound notification after each task Now you
                            can scroll X while waiting and never miss when it's done. Let's go 👇
                        </p>
                        <div class="mcp-usecase-video">
                            <video class="video" controls>
                                <source
                                    src="https://video.twimg.com/amplify_video/1906305630216069120/vid/avc1/480x270/
Aqu0mkHXqTm2A8KA.mp4?tag=14"
                                    type="video/mp4"
                                />
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</template>

<script setup></script>

<style scoped lang="scss">
.mcp-usecases-main {
    overflow: hidden;
}

.mcp-usecases-section {
    padding: 4rem 0;
}

.mcp-usecases-container {
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 2rem;
}

.mcp-usecases-header {
    text-align: center;
}

.mcp-usecases-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #ea580c;
    margin-bottom: 0.5rem;
}

.mcp-usecases-desc {
    font-size: 1.25rem;
    color: #4b5563;
}

.mcp-usecases-cards {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    margin-top: 2rem;
    scroll-snap-type: x mandatory;
}

.mcp-usecase-card {
    min-width: 300px;
    max-width: 350px;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    flex-shrink: 0;
    scroll-snap-align: center;
    background: #fff;
    display: flex;
    flex-direction: column;
}

.mcp-usecase-card-header {
    display: flex;
    align-items: center;
}

.avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    margin-right: 0.5rem;
}

.user-row {
    display: flex;
    align-items: center;
}

.user-name {
    font-weight: 500;
}

.icon-verified {
    width: 1rem;
    height: 1rem;
    color: #3b82f6;
    margin-left: 0.25rem;
}

.user-id {
    font-size: 0.875rem;
    color: #6b7280;
}

.user-link {
    margin-left: auto;
    color: #60a5fa;
}

.icon-link {
    width: 1.25rem;
    height: 1.25rem;
}

.mcp-usecase-card-desc {
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    max-width: 100%;
}

.mcp-usecase-list {
    list-style-type: disc;
    padding-left: 1.25rem;
    margin-bottom: 0.75rem;
}

.mcp-usecase-video {
    margin-top: 10px;
    height: 150px;
}

.video {
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    height: 100%;
    max-width: 100%;
    width: 100%;
}
</style>
