<template>
    <div class="search-input-wrapper" :class="customClass">
        <span class="search-icon">
            <i class="pi pi-search"></i>
        </span>
        <InputText
            v-model="searchValue"
            :placeholder="placeholder"
            class="search-input"
            :style="inputStyle"
            @input="handleInput"
        />
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import InputText from 'primevue/inputtext';
import { useHomeStore } from '@/stores/home-store';

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '搜索...',
    },
    delay: {
        type: Number,
        default: 300,
    },
    customClass: {
        type: String,
        default: '',
    },
    width: {
        type: [String, Number],
        default: '220px',
    },
    expandedWidth: {
        type: [String, Number],
        default: '260px',
    },
    height: {
        type: [String, Number],
        default: '38px',
    },
    backgroundColor: {
        type: String,
        default: '#f5f7fa',
    },
    focusBackgroundColor: {
        type: String,
        default: '#eef2f7',
    },
    borderRadius: {
        type: [String, Number],
        default: '20px',
    },
});

const emit = defineEmits(['update:modelValue', 'search']);

const homeStore = useHomeStore();

const searchValue = ref(props.modelValue);
const timer = ref(null);

// 监听输入值变化并同步到modelValue和store
watch(
    () => props.modelValue,
    (newValue) => {
        searchValue.value = newValue;
        homeStore.setKeyword(newValue);
    }
);

// 监听内部值变化并更新modelValue和store
watch(searchValue, (newValue) => {
    emit('update:modelValue', newValue);
    homeStore.setKeyword(newValue);
});

// 计算输入框样式
const inputStyle = computed(() => {
    return {
        width: props.width,
        height: props.height,
        borderRadius:
            typeof props.borderRadius === 'number' ? `${props.borderRadius}px` : props.borderRadius,
        backgroundColor: props.backgroundColor,
    };
});

// 处理输入事件，增加防抖
const handleInput = () => {
    if (timer.value) {
        clearTimeout(timer.value);
    }

    timer.value = setTimeout(() => {
        emit('search', searchValue.value);
    }, props.delay);
};
</script>

<style scoped lang="scss">
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;

    .search-icon {
        position: absolute;
        left: 12px;
        z-index: 1;
        color: #5e6d82;
    }

    .search-input {
        padding-left: 35px;
        background-color: v-bind('props.backgroundColor');
        border: none;
        border-radius: v-bind(
            'typeof props.borderRadius === "number" ? `${props.borderRadius}px` : props.borderRadius'
        );
        height: v-bind('typeof props.height === "number" ? `${props.height}px` : props.height');
        width: v-bind('typeof props.width === "number" ? `${props.width}px` : props.width');
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
            background-color: v-bind('props.focusBackgroundColor');
        }

        &:focus {
            background-color: v-bind('props.focusBackgroundColor');
            width: v-bind(
                'typeof props.expandedWidth === "number" ? `${props.expandedWidth}px` : props.expandedWidth'
            );
            outline: none;
        }
    }
}
</style>
