<template>
    <div class="dlrStatus">
        <div
            class="dlrStatus-pic"
            :class="{
                empty: isEmpty,
                failed: isFailed,
                loading: isLoading,
                network: isNetwork,
            }"
        ></div>
        <div v-if="statusTitleVisible" class="dlrStatus-title">
            <p>{{ statusTitle }}</p>
        </div>
        <div class="dlrStatus-text">
            <p>{{ statusText }}</p>
        </div>
        <div v-if="hasBtn" class="dlrStatus-btn">
            <a href="javascript:void(0)">
                <p>{{ btnText }}</p>
            </a>
        </div>
    </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
    isEmpty: {
        type: Boolean,
        default: true,
    },
    isFailed: {
        type: Boolean,
        default: false,
    },
    isLoading: {
        type: Boolean,
        default: false,
    },
    isNetwork: {
        type: Boolean,
        default: false,
    },
    hasBtn: {
        type: Boolean,
        default: false,
    },
    btnText: {
        type: String,
        default: '重试',
    },
    statusText: {
        type: String,
        default: '暂无数据',
    },
    statusTitleVisible: {
        type: Boolean,
        default: false,
    },
    statusTitle: {
        type: String,
        default: '暂无数据',
    },
});
</script>

<style lang="scss" scoped>
.dlrStatus {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;

    &-pic {
        width: 10rem;
        height: 10rem;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 100%;

        &.empty {
            background-image: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/m_dir/ics/fs_subsidy/img/pic/nodata-loog-7bda4b424f.png);
        }

        &.loading {
            width: 5rem;
            height: 5rem;
            background-image: url(https://fs.autohome.com.cn/dealer_views/dealer_front/public/athm_loong/athm_loong_loading.gif);
        }

        &.network {
            background-image: url(https://x.autoimg.cn/dealer/dealer_front/m/publicimg/dlrstate/0.0.0/athm_loong_network.png);
        }
    }

    &-title {
        font-size: 1.2rem;
        font-weight: bold;
        text-align: center;
        line-height: 1.8rem;
        margin-top: 1rem;

        p {
            margin: 0;
            padding: 0;
        }
    }

    &-text {
        font-size: 1rem;
        line-height: 1.4rem;
        margin-top: 0.8rem;

        p {
            margin: 0;
            padding: 0;
        }
    }

    &-btn {
        text-align: center;
        margin-top: 1rem;

        a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 10.4rem;
            height: 3.2rem;
            background: #fb384d;
            background-image: linear-gradient(90deg, #ff7281 1%, #fb3a4b 100%);
            border-radius: 3.2rem;
            font-size: 1.4rem;
            color: #fff;
            font-weight: bold;
        }
    }
}
</style>
