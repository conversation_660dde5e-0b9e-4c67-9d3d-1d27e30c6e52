<script setup>
import { computed, onMounted } from 'vue';

const props = defineProps({
    /**
     * 错误信息内容
     */
    error: {
        type: [String, Error, Object],
        default: null,
    },
    /**
     * 错误标题
     */
    title: {
        type: String,
        default: '请求失败',
    },
});

// 格式化错误信息
const errorMessage = computed(() => {
    if (!props.error) {
        return '';
    }

    if (typeof props.error === 'string') {
        return props.error;
    }

    if (props.error instanceof Error) {
        return props.error.message || '未知错误';
    }

    if (typeof props.error === 'object') {
        return props.error.message || props.error.toString() || '未知错误';
    }

    return '未知错误';
});

onMounted(() => {
    console.log(props.error);
});
</script>

<template>
    <transition name="api-error-fade">
        <Message
            severity="error"
            :closable="true"
            class="api-error-message w-full p-3 border-round-sm"
        >
            <div class="flex align-items-center">
                <span class="text-sm">{{ errorMessage }}</span>
            </div>
        </Message>
    </transition>
</template>

<style lang="scss" scoped>
.api-error-fade-enter-active,
.api-error-fade-leave-active {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.api-error-fade-enter-from,
.api-error-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.api-error-message {
    margin-top: 0;
    margin-bottom: 1rem;

    :deep(.p-message-wrapper) {
        padding: 0.5rem;
    }
}
</style>
