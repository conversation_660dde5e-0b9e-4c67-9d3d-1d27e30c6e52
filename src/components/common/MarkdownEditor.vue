<template>
    <div class="markdown-editor" :style="{ height: height }">
        <!-- 编辑器标题栏 -->
        <div class="editor-header">
            <div class="header-left">
                <i class="pi pi-file-edit"></i>
                <span>Markdown 编辑器</span>
            </div>
            <div class="header-right">
                <Button
                    icon="pi pi-eye"
                    :class="['view-toggle', { active: showPreview }]"
                    size="small"
                    text
                    @click="togglePreview"
                />
            </div>
        </div>

        <!-- 编辑器主体 -->
        <div class="editor-body">
            <!-- 编辑区域 -->
            <div :class="['editor-panel', { 'full-width': !showPreview }]">
                <div class="panel-header">
                    <span class="panel-title">编辑</span>
                </div>
                <Textarea
                    v-model="localValue"
                    class="markdown-textarea"
                    :placeholder="placeholder"
                    :disabled="disabled"
                    @input="handleInput"
                />
            </div>

            <!-- 预览区域 -->
            <div v-if="showPreview" class="preview-panel">
                <div class="panel-header">
                    <span class="panel-title">预览</span>
                </div>
                <div class="markdown-preview" v-html="renderedMarkdown"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import MarkdownIt from 'markdown-it';
import 'github-markdown-css/github-markdown.css';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

// Props
const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    height: {
        type: String,
        default: '500px'
    },
    placeholder: {
        type: String,
        default: '请输入 Markdown 内容...'
    },
    disabled: {
        type: Boolean,
        default: false
    },
    previewByDefault: {
        type: Boolean,
        default: true
    }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// 响应式数据
const localValue = ref(props.modelValue);
const showPreview = ref(props.previewByDefault);

// Markdown 解析器初始化
const md = new MarkdownIt({
    html: true, // 允许 HTML 标签
    linkify: true, // 自动识别链接
    typographer: true, // 启用智能引号和其他印刷增强
    breaks: true, // 将换行符转换为 <br>
    highlight: (code, lang) => {
        if (lang && hljs.getLanguage(lang)) {
            try {
                return hljs.highlight(code, { language: lang }).value;
            } catch (__) {
                console.log('highlight error', __);
            }
        }
        return '';
    }
});

// 计算属性：渲染的 Markdown
const renderedMarkdown = computed(() => {
    if (!localValue.value) {
        return '<div class="empty-hint">内容将在这里预览...</div>';
    }
    try {
        return md.render(localValue.value);
    } catch (error) {
        console.error('Markdown 渲染错误:', error);
        return '<div class="error-hint">Markdown 渲染出错</div>';
    }
});

// 方法
const handleInput = () => {
    emit('update:modelValue', localValue.value);
};

const togglePreview = () => {
    showPreview.value = !showPreview.value;
};

// 监听外部值变化
watch(
    () => props.modelValue,
    newValue => {
        if (newValue !== localValue.value) {
            localValue.value = newValue;
        }
    }
);
</script>

<style scoped lang="scss">
.markdown-editor {
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    background: white;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: var(--surface-50);
    border-bottom: 1px solid var(--surface-border);

    .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);

        .pi {
            color: var(--primary-color);
        }
    }

    .header-right {
        .view-toggle {
            color: var(--text-color-secondary);
            transition: all 0.2s ease;

            &.active {
                color: var(--primary-color);
                background-color: var(--primary-50);
            }

            &:hover {
                color: var(--primary-color);
            }
        }
    }
}

.editor-body {
    display: flex;
    flex: 1;
    height: calc(100% - 48px);
}

.editor-panel {
    width: 50%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--surface-border);
    box-sizing: border-box;
    &.full-width {
        border-right: none;
    }
}

.preview-panel {
    width: 50%;
    display: flex;
    flex-direction: column;
    word-break: break-all;
    box-sizing: border-box;
}

.panel-header {
    padding: 8px 16px;
    background-color: var(--surface-25);
    border-bottom: 1px solid var(--surface-border);

    .panel-title {
        font-size: 12px;
        font-weight: 500;
        color: var(--text-color-secondary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
}

.markdown-textarea {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    line-height: 1.6;
    padding: 16px;
    background: transparent;

    &:focus {
        box-shadow: none;
    }

    &::placeholder {
        color: var(--text-color-secondary);
        font-style: italic;
    }
}

.markdown-preview {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-color);

    .empty-hint,
    .error-hint {
        color: var(--text-color-secondary);
        font-style: italic;
        text-align: center;
        margin-top: 40px;
    }

    .error-hint {
        color: var(--red-500);
    }

    // Markdown 预览样式
    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
        margin: 16px 0 8px 0;
        font-weight: 600;
        line-height: 1.4;
    }

    :deep(h1) {
        font-size: 24px;
        border-bottom: 1px solid var(--surface-border);
        padding-bottom: 8px;
    }

    :deep(h2) {
        font-size: 20px;
    }

    :deep(h3) {
        font-size: 18px;
    }

    :deep(h4) {
        font-size: 16px;
    }

    :deep(h5) {
        font-size: 14px;
    }

    :deep(h6) {
        font-size: 12px;
    }

    :deep(p) {
        margin: 12px 0;
    }

    :deep(ul),
    :deep(ol) {
        margin: 12px 0;
        padding-left: 24px;
    }

    :deep(li) {
        margin: 4px 0;
    }

    :deep(blockquote) {
        margin: 16px 0;
        padding: 8px 16px;
        border-left: 4px solid var(--primary-color);
        background-color: var(--surface-50);
        color: var(--text-color-secondary);
    }

    :deep(code) {
        background-color: var(--surface-100);
        padding: 2px 4px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 12px;
    }

    :deep(pre) {
        background-color: var(--surface-100);
        padding: 16px;
        border-radius: 8px;
        overflow-x: auto;
        margin: 16px 0;

        code {
            background: none;
            padding: 0;
        }
    }

    :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 16px 0;
    }

    :deep(th),
    :deep(td) {
        border: 1px solid var(--surface-border);
        padding: 8px 12px;
        text-align: left;
    }

    :deep(th) {
        background-color: var(--surface-50);
        font-weight: 600;
    }

    :deep(a) {
        color: var(--primary-color);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 8px 0;
    }

    .editor-panel {
        width: 50%;
        flex-shrink: 0;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        border-right: 1px solid var(--surface-border);

        &.full-width {
            border-right: none;
        }
    }

    .preview-panel {
        width: 50%;
        flex-shrink: 0;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
    }

    .panel-header {
        padding: 8px 16px;
        background-color: var(--surface-25);
        border-bottom: 1px solid var(--surface-border);

        .panel-title {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-color-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

    .markdown-textarea {
        flex: 1;
        border: none;
        outline: none;
        resize: none;
        font-size: 14px;
        line-height: 1.6;
        padding: 16px;
        background: transparent;

        &:focus {
            box-shadow: none;
        }

        &::placeholder {
            font-size: 12px;
            color: var(--text-color-secondary);
        }
    }

    .markdown-preview {
        padding: 16px;
        overflow-y: auto;
        font-size: 14px;
        line-height: 1.6;
        color: var(--text-color);

        .empty-hint,
        .error-hint {
            color: var(--text-color-secondary);
            font-style: italic;
            text-align: center;
            margin-top: 40px;
        }

        .error-hint {
            color: var(--red-500);
        }

        // Markdown 预览样式
        :deep(h1),
        :deep(h2),
        :deep(h3),
        :deep(h4),
        :deep(h5),
        :deep(h6) {
            margin: 16px 0 8px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        :deep(h1) {
            font-size: 24px;
            border-bottom: 1px solid var(--surface-border);
            padding-bottom: 8px;
        }

        :deep(h2) {
            font-size: 20px;
        }

        :deep(h3) {
            font-size: 18px;
        }

        :deep(h4) {
            font-size: 16px;
        }

        :deep(h5) {
            font-size: 14px;
        }

        :deep(h6) {
            font-size: 12px;
        }

        :deep(p) {
            margin: 12px 0;
        }

        :deep(ul),
        :deep(ol) {
            margin: 12px 0;
            padding-left: 24px;
        }

        :deep(li) {
            margin: 4px 0;
        }

        :deep(blockquote) {
            margin: 16px 0;
            padding: 8px 16px;
            border-left: 4px solid var(--primary-color);
            background-color: var(--surface-50);
            color: var(--text-color-secondary);
        }

        :deep(code) {
            background-color: var(--surface-100);
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
        }

        :deep(pre) {
            background-color: var(--surface-100);
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 16px 0;

            code {
                background: none;
                padding: 0;
            }
        }
    }
}
</style>
