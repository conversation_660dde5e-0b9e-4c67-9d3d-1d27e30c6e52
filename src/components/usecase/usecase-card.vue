<template>
    <div class="server-card">
        <div class="card-header">
            <div class="server-title">
                <div class="server-title-left">
                    <div class="flex align-items-center">
                        <h3 class="name">{{ usecase.name }}</h3>
                        <Tag
                            v-if="usecase.source"
                            :value="getEnumName('SourceType', usecase.source)"
                            :severity="getSourceSeverity(usecase.source)"
                            size="small"
                            class="type-tag"
                        />
                    </div>
                    <p class="supplier">{{ usecase.author }}</p>
                </div>
                <p class="server-logo"><img :src="usecase.logoUrl" alt="logo" /></p>
            </div>
        </div>

        <div class="card-content">
            <div class="card-description">
                <p>{{ usecase.description || usecase.introduction }}</p>
            </div>
            <div class="language-section">
                <div v-if="usecase.publishTime" class="language">
                    <span>{{ formatDate(usecase.publishTime) }}</span>
                </div>
                <Button
                    v-if="usecase.videoUrl"
                    icon="pi pi-video"
                    size="small"
                    text
                    rounded
                    @click.stop="openVideoUrl(usecase.videoUrl)"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { getEnumName } from '@/enums/sourceType';

defineProps({
    usecase: {
        type: Object,
        required: true,
        default: () => ({
            name: '',
            source: '',
            description: '',
            introduction: '',
            author: '',
            type: 0,
            videoUrl: '',
            modifiedStime: '',
        }),
    },
});

// 获取用例类型名称
// const getUseCaseTypeName = (type) => {
//     const typeMap = {
//         0: '未知',
//         1: '通用',
//         2: '专业',
//         3: '教程',
//     };
//     return typeMap[type] || '其他';
// };

// 获取来源对应的样式
const getSourceSeverity = (source) => {
    const severityMap = {
        0: 'success',
        1: 'primary',
        2: 'warning',
        3: 'danger',
    };
    return severityMap[source] || 'primary';
};

// 格式化日期
const formatDate = (dateStr) => {
    if (!dateStr) {
        return '';
    }

    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr;
        }

        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    } catch (e) {
        return dateStr;
    }
};

// 打开视频链接
const openVideoUrl = (url) => {
    window.open(url, '_blank');
};
</script>

<!-- <style scoped lang="scss">
    .usecase-card {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--surface-200);
        min-height: 240px;

        &:hover {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
    }

    .card-header {
        padding: 1.25rem;
        background: linear-gradient(135deg,
                var(--primary-color-lighter, #f0f7ff) 0%,
                var(--surface-100) 100%);
        border-bottom: 1px solid var(--surface-200);
        height: 60px;
        display: flex;
        align-items: center;
    }

    .usecase-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
        width: 100%;

        .name {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }
    }

    .card-content {
        padding: 0.75rem 1.25rem 0.75rem;
        flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 60px);
    }

    .card-description {
        flex: 1;
        margin-bottom: 0.75rem;
        overflow: hidden;

        p {
            margin: 0;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--text-color-secondary);
        }
    }

    .video-section {
        display: flex;
        justify-content: center;
        margin-bottom: 1rem;

        .video-button {
            width: 100%;
        }
    }

    .card-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        border-top: 1px solid var(--surface-200);
        padding-top: 0.75rem;

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            i {
                font-size: 0.75rem;
            }
        }

        .meta-info {
            display: flex;
            gap: 0.75rem;
            align-items: center;

            .author {
                background-color: var(--surface-100);
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
            }
        }
    }
</style> -->
