<template>
    <div class="detail-header">
        <div class="header-main">
            <div v-if="detailData.logoUrl" class="logo">
                <img :src="detailData.logoUrl" :alt="detailData.name" />
            </div>
            <div class="info">
                <div class="title-row flex align-items-center justify-content-between">
                    <div class="flex align-items-center gap-2">
                        <h1 class="title">{{ detailData.name }}</h1>
                        <Tag
                            v-if="detailData.source"
                            :value="getSourceName(detailData.source)"
                            :severity="getSourceSeverity(detailData.source)"
                            class="type-tag"
                        />
                    </div>
                    <div class="header-actions">
                        <slot name="actions"></slot>
                    </div>
                </div>
                <p v-if="detailData.description" class="description">
                    {{ detailData.description }}
                </p>
                <div class="meta">
                    <slot name="meta"></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getEnumName, getSourceSeverity } from '@/enums/sourceType';

defineProps({
    detailData: {
        type: Object,
        required: true,
    },
});

const getSourceName = (source) => {
    return getEnumName('SourceType', source);
};
</script>

<style lang="scss" scoped>
.detail-header {
    .header-main {
        display: flex;
        gap: 16px;

        .logo {
            width: 64px;
            height: 64px;
            flex-shrink: 0;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                border-radius: 8px;
            }
        }

        .info {
            flex: 1;
            min-width: 0;

            .title-row {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 8px;

                .title {
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                    color: var(--text-color);
                }
            }

            .description {
                margin: 0 0 16px;
                color: var(--text-color-secondary);
                font-size: 14px;
                line-height: 1.5;
            }

            .meta {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }
        }
    }
}

@media (max-width: 768px) {
    .detail-header {
        .header-main {
            flex-direction: column;
            align-items: center;
            text-align: center;

            .logo {
                width: 48px;
                height: 48px;
            }

            .info {
                .title-row {
                    justify-content: center;
                }
            }
        }

        .header-actions {
            justify-content: center;
        }
    }
}
</style>
