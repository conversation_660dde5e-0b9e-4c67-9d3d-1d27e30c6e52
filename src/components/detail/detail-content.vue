<template>
    <div class="detail-content">
        <div class="content-header" v-if="false">
            <SelectButton
                v-model="selectedView"
                :options="viewOptions"
                option-label="label"
                option-value="value"
            />
        </div>

        <div class="content-body">
            <div v-show="selectedView === 'original'" class="markdown-body">
                <div v-html="compiledMarkdown"></div>
            </div>
            <div v-show="selectedView === 'translated'" class="markdown-body">
                <div id="translateDom" v-html="compiledMarkdown"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import translate from 'i18n-jsautotranslate';
import 'github-markdown-css/github-markdown.css';
import 'highlight.js/styles/github.css';

const props = defineProps({
    content: {
        type: String,
        default: ''
    }
});

// Markdown 渲染器配置
const md = new MarkdownIt({
    html: true,
    breaks: true,
    linkify: true,
    highlight: (code, lang) => {
        if (lang && hljs.getLanguage(lang)) {
            try {
                return hljs.highlight(code, { language: lang }).value;
            } catch (__) {
                console.log('highlight error', __);
            }
        }
        return '';
    }
});

// 视图选项
const viewOptions = [
    { label: '原文', value: 'original' },
    { label: '译文', value: 'translated' }
];

const selectedView = ref('original');

// 编译 Markdown
const compiledMarkdown = computed(() => {
    // 3. 如果后端直接返回 utf-8 字符串
    const decoded = props.content;
    console.log(typeof props.content);
    return md.render(decoded);
});

// 翻译相关
let currentLanguage = 'chinese_simplified';

const translateContent = () => {
    if (currentLanguage !== 'chinese_simplified') {
        const translateElement = document.getElementById('translateDom');
        if (translateElement) {
            translate.language.translateLocal = true;
            translate.language.setDefaultTo('chinese_simplified');
            translate.execute(translateElement);
        }
    }
};

onMounted(() => {
    if (props.content) {
        currentLanguage = translate.language.recognition(props.content).languageName;
        translateContent();
    }
});
</script>

<style lang="scss" scoped>
.detail-content {
    .content-header {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 16px;
    }

    .content-body {
        .markdown-body {
            padding: 0 12px;
        }
    }
}

// 深色模式样式覆盖
@media (prefers-color-scheme: dark) {
    .markdown-body {
        color-scheme: light;
        --focus-outlineColor: #0969da;
        --fgColor-default: #1f2328;
        --fgColor-muted: #59636e;
        --fgColor-accent: #0969da;
        --fgColor-success: #1a7f37;
        --fgColor-attention: #9a6700;
        --fgColor-danger: #d1242f;
        --fgColor-done: #8250df;
        --bgColor-default: #ffffff;
        --bgColor-muted: #f6f8fa;
        --bgColor-neutral-muted: #818b981f;
        --bgColor-attention-muted: #fff8c5;
        --borderColor-default: #d1d9e0;
        --borderColor-muted: #d1d9e0b3;
        --borderColor-neutral-muted: #d1d9e0b3;
        --borderColor-accent-emphasis: #0969da;
        --borderColor-success-emphasis: #1a7f37;
        --borderColor-attention-emphasis: #9a6700;
        --borderColor-danger-emphasis: #cf222e;
        --borderColor-done-emphasis: #8250df;
    }
}
</style>
