<template>
    <div class="detail-layout">
        <div v-if="detailData" class="detail-header">
            <slot name="header"></slot>
        </div>
        <div v-if="detailData" class="detail-contentWrap">
            <Card class="detail-content">
                <template #title>
                    <TabView v-model:active-index="activeTabIndex" class="detail-tabs">
                        <TabPanel v-for="tab in tabs" :key="tab.key" :header="tab.title">
                            <slot :name="tab.key" :tab="tab"></slot>
                        </TabPanel>
                    </TabView>
                </template>
            </Card>
            <slot name="config"></slot>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
    detailData: {
        type: Object,
        default: null
    },
    tabs: {
        type: Array,
        required: true
    },
    defaultTab: {
        type: String,
        default: undefined
    }
});

const activeTabIndex = ref(0);

// 监听默认标签页变化
watch(
    () => props.defaultTab,
    newTab => {
        if (newTab) {
            const index = props.tabs.findIndex(tab => tab.key === newTab);
            if (index !== -1) {
                activeTabIndex.value = index;
            }
        }
    },
    { immediate: true }
);
</script>

<style lang="scss" scoped>
/*布局*/
.mcp_page-layout {
    margin: 20px auto;
}

/*头部*/
.detail_top {
    background-color: #f7f9fd;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    margin-bottom: 24px;

    .logo {
        height: 42px;
        margin-right: 12px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    &-con {
        flex: 1;

        .main {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;

            .name {
                display: flex;
                align-items: center;
                color: #27254c;
                font-size: 16px;
                font-weight: bold;
            }

            .tags {
                display: flex;
                align-items: center;
                white-space: nowrap;
                justify-content: center;

                .pi {
                    font-size: 12px;
                    margin-right: 4px;
                }

                em {
                    margin-left: 8px;
                    height: 20px;
                    line-height: 20px;
                    border-radius: 22px;
                    background: rgba(0, 200, 83, 0.1);
                    font-size: 12px;
                    color: #00a854;
                    padding: 0 8px;
                    font-weight: normal;
                    margin-right: 8px;

                    &:last-child {
                        margin-right: 0;
                    }
                }
            }
        }

        .sub {
            .subTit {
                color: #8284a4;
                font-size: 14px;
            }

            .other {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .git-url {
                    color: #fff;
                    font-size: 12px;
                }
            }

            .supplier {
                color: #8284a4;
                font-size: 12px;
            }
        }
    }
}

.detail_label {
    height: 20px;
    border-radius: 4px;
    background: #ffffff;
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0em;
    font-variation-settings: 'opsz' auto;
    color: #464d5b;
    padding: 0 8px;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    margin-left: 12px;
    vertical-align: middle;

    .pi {
        display: inline-block;
        vertical-align: middle;
        font-size: 12px;
        margin-right: 4px;
    }
}

/*改写默认*/
:deep(.custom_selectBtn) {
    text-align: right;
    .p-button {
        padding: 6px 10px;
        font-size: 13px !important;
    }
}
:deep(.p-selectbutton .p-button.p-highlight) {
    color: #3b82f6;
}

:deep(.custom_tab .p-tabview-panels) {
    padding: 0;
}
/*
:deep(.p-tooltip){
    font-size: 12px !important;
}
*/
:deep(.p-tabview-nav) {
    font-size: 16px;
}

/*宽度断点*/
@media (max-width: 1440px) {
    .mcp_page-layout {
        width: 1280px;
        padding: 0;
    }
}

@media (max-width: 1919px) {
    .mcp_page-layout {
        width: auto;
        padding: 0 80px;
    }
}

@media (min-width: 1920px) {
    .mcp_page-layout {
        width: 1760px;
        padding: 0;
    }
}
.detail-layout {
    padding-top: 1.5rem;

    .detail-header {
        background: var(--surface-card);
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--surface-200);
        border-radius: 8px;
    }

    .detail-content {
        background: var(--surface-card);
        border-radius: 8px;
        box-shadow: var(--card-shadow);

        :deep(.p-card-body) {
            padding: 0;
        }
    }

    .detail-tabs {
        :deep(.p-tabview-nav) {
            border-bottom: 1px solid var(--surface-border);
            padding: 0 24px;
            border-radius: 8px 8px 0 0;
        }

        :deep(.p-tabview-panels) {
            padding: 24px;
        }
    }
    .detail-contentWrap {
        display: flex;
        .detail-content {
            flex: 1;
            width: 1%;
            border: 1px solid var(--surface-200);
        }
    }
}

@media (max-width: 768px) {
    .detail-layout {
        padding: 0 12px;
        margin: 12px auto;

        .detail-header {
            padding: 16px;
            margin-bottom: 16px;
        }

        .detail-tabs {
            :deep(.p-tabview-nav) {
                padding: 0 16px;
            }

            :deep(.p-tabview-panels) {
                padding: 16px;
            }
        }
    }
}
</style>
