<template>
    <div class="filter-container">
        <div
            v-for="(item, index) in filterItems"
            :key="index"
            class="filter-item"
            :class="{ active: selectedItem === item }"
            @click="selectFilter(item)"
        >
            <div class="filter-name">{{ item.name }}</div>
            <div class="filter-count">{{ item.count }}</div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

// 过滤器数据
const filterItems = ref([
    { name: '浏览器自动化', count: 199 },
    { name: '搜索工具', count: 357 },
    { name: '交流协作工具', count: 151 },
    { name: '开发者工具', count: 759 },
    { name: '娱乐与多媒体', count: 37 },
    { name: '文件系统', count: 131 },
    { name: '金融', count: 129 },
    { name: '知识管理与记忆', count: 177 },
    { name: '位置服务', count: 26 },
]);

// 当前选中的项目
const selectedItem = ref(null);

// 暴露选择事件
const emit = defineEmits(['filter-selected']);

// 选择过滤器项
const selectFilter = (item) => {
    selectedItem.value = item;
    emit('filter-selected', item);
};

// 暴露给父组件的属性和方法
defineExpose({
    filterItems,
    selectFilter,
    selectedItem,
});
</script>

<style scoped lang="scss">
.filter-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 250px;
}

.filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #e6f3ff;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background-color: #d6ebff;
    }

    &.active {
        background-color: #c2e0ff;
    }
}

.filter-name {
    color: #333333;
    font-size: 14px;
}

.filter-count {
    background-color: #1890ff;
    color: #ffffff;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    min-width: 30px;
    text-align: center;
}
</style>
