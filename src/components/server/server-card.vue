<template>
    <div class="server-card" @click="goDetail">
        <div class="card-header">
            <div class="server-title">
                <div class="server-title-left">
                    <div class="flex align-items-center">
                        <h3 class="name ellipsis" :title="server.name">{{ server.name }}</h3>
                        <Tag
                            v-if="server.source"
                            :value="getEnumName('SourceType', server.source)"
                            :severity="getServerSourceSeverity(server.source)"
                            class="type-tag"
                        />
                    </div>
                    <p class="supplier" v-if="server.submitter !== 'admin'">
                        @{{ server.submitter }}
                    </p>
                    <p class="supplier" v-else>{{ server.supplier }}</p>
                </div>
                <p class="server-logo"><img :src="server.logoUrl" alt="logo" /></p>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description">
                <p>{{ server.description || server.introduction }}</p>
            </div>
            <div class="card-meta">
                <span v-if="server.modifiedStime" class="meta-item">
                    <i class="pi pi-calendar"></i>
                    <span>{{ formatDate(server.modifiedStime) }}</span>
                </span>

                <div class="meta-stats">
                    <span v-if="server.downloadCount" class="meta-item downloads">
                        <i class="pi pi-download"></i>
                        <span>{{ formatNumber(server.downloadCount) }}</span>
                    </span>

                    <span v-if="server.gitStarCount" class="meta-item stars">
                        <i class="pi pi-star"></i>
                        <span>{{ formatNumber(server.gitStarCount) }}</span>
                    </span>
                </div>
            </div>
            <div class="language-section">
                <div v-if="server.programLanguage" class="language">
                    <span
                        class="language-dot"
                        :style="{ backgroundColor: getLanguageColor(server.programLanguage) }"
                    ></span>
                    <span>{{ server.programLanguage }}</span>
                </div>
                <div
                    v-if="server.gitUrl && server.gitUrl.length > 0"
                    class="git-repo"
                    @click.stop="openGitUrl(server.gitUrl)"
                >
                    <i v-if="gitRepoType === GitRepoType.GITHUB" class="pi pi-github"></i>
                    <img
                        v-else-if="gitRepoType === GitRepoType.CORP"
                        src="@/assets/img/gitlab.png"
                        alt="GitLab"
                        class="git-icon"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getEnumName, getSourceSeverity } from '@/enums/sourceType';
import { useRouter } from 'vue-router';
import { getLanguageColor } from '@/enums/language';
import { formatDate, getGitRepoType, GitRepoType } from '@/utils/commonUtils';
import { computed } from 'vue';

const props = defineProps({
    server: {
        type: Object,
        required: true,
        default: () => ({
            name: '',
            source: '',
            category: '',
            gitUrl: '',
            description: '',
            introduction: '',
            tags: '',
            programLanguage: '',
            gitStarCount: '',
            downloadCount: '',
            modifiedStime: '',
            id: '',
        }),
    },
});

const router = useRouter();
const goDetail = () => {
    if (props.server && props.server.id) {
        router.push(`/servers/detail/${props.server.id}`);
    }
};

// 解析标签字符串为数组
// const parseTags = (tagsString) => {
//     if (!tagsString) return [];
//     // 如果已经是数组，直接返回
//     if (Array.isArray(tagsString)) return tagsString;
//     // 尝试分割字符串
//     try {
//         return tagsString
//             .split(',')
//             .map((tag) => tag.trim())
//             .filter((tag) => tag);
//     } catch (e) {
//         console.error('解析标签失败', e);
//         return [];
//     }
// };

// 获取来源对应的样式
const getServerSourceSeverity = (source) => {
    return getSourceSeverity(source, 'server');
};

// 格式化数字显示
const formatNumber = (num) => {
    if (!num) {
        return '0';
    }

    const n = parseInt(num, 10);
    if (isNaN(n)) {
        return '0';
    }

    if (n >= 1000) {
        return (n / 1000).toFixed(1) + 'k';
    }

    return n.toString();
};

// 打开仓库链接
const openGitUrl = (url) => {
    window.open(url, '_blank');
};

// 获取Git仓库类型
const gitRepoType = computed(() => {
    return getGitRepoType(props.server.gitUrl);
});
</script>

<style scoped lang="scss"></style>
