<template>
    <div class="mcp-top-bar">
        <div class="top-bar-content container">
            <div class="logo" @click="goToHomePage">
                <img class="logo-img" src="@/assets/img/logo-l-blue.png" alt="logo" />
            </div>
            <div class="navigation flex">
                <div class="nav-items">
                    <Button
                        v-for="item in menuItems"
                        :key="item.label"
                        :label="item.label"
                        :class="['p-button-text nav-button', { active: isActive(item.path) }]"
                        @click="item.command"
                    />
                </div>
                <div class="user-actions">
                    <Button
                        label="提交MCP"
                        icon="pi pi-plus-circle"
                        class="p-button-rounded p-button-text notification-btn"
                        @click="navigateTo('/submit')"
                    />
                    <Button
                        label="有建议点这里"
                        icon="pi pi-comment"
                        class="p-button-rounded p-button-text notification-btn"
                        @click="navigatorToGitlabIssue"
                    />
                    <div ref="userAvatarRef" class="user-avatar-wrapper">
                        <Avatar
                            :label="userName.charAt(0)"
                            shape="circle"
                            size="normal"
                            class="user-avatar"
                            @click="toggleUserMenu"
                        />
                        <div
                            v-if="isMenuVisible"
                            class="custom-menu-dropdown"
                            :style="menuPosition"
                        >
                            <div class="menu-header">
                                <Avatar
                                    :label="userName.charAt(0)"
                                    shape="circle"
                                    size="large"
                                    class="menu-avatar"
                                />
                                <div class="user-info">
                                    <span class="user-display-name">{{ userName }}</span>
                                </div>
                            </div>
                            <div class="menu-items">
                                <div class="menu-item" @click="navigateTo('/my-servers')">
                                    <img
                                        src="https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/logo-s-blue-m.png"
                                        alt=""
                                        srcset=""
                                    />
                                    <span>我的MCP</span>
                                </div>
                                <div class="menu-item" @click="navigateTo('/my-apikeys')">
                                    <i class="pi pi-key" style="font-weight: bold"></i>
                                    <span>我的API Keys</span>
                                </div>
                                <div class="menu-item logout-item" @click="handleLogout">
                                    <i class="pi pi-sign-out"></i>
                                    <span>退出登录</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import Button from 'primevue/button';
import Avatar from 'primevue/avatar';
import { useRouter, useRoute } from 'vue-router';
import { useHomeStore } from '@/stores/home-store';

const homeStore = useHomeStore();
const router = useRouter();
const route = useRoute();

// 跳转到首页
const goToHomePage = () => {
    router.push('/');
};

// 菜单项
const menuItems = ref([
    {
        label: 'MCP Server',
        path: '/servers',
        command: () => {
            // 清空所有搜索和筛选条件
            homeStore.clearKeyword();
            homeStore.clearSource();

            // 导航到 servers 页面，添加刷新参数
            router.push({
                path: '/servers',
                query: { refresh: Date.now() }
            });
        }
    },
    {
        label: 'MCP Client',
        path: '/clients',
        command: () => router.push('/clients')
    },
    {
        label: 'IDE 插件',
        path: '/ide-plugins',
        command: () => router.push('/ide-plugins')
    },
    {
        label: 'MCP 网关',
        path: '/mcp-gateway',
        command: () => router.push('/mcp-gateway')
    },
    // {
    //     label: '使用案例',
    //     path: '/case-studies',
    //     command: () => router.push('/case-studies'),
    // },
    {
        label: '在线调试',
        path: '/inspector',
        command: () => router.push('/inspector')
    },

    {
        label: '在线试用',
        path: '/playground',
        command: () => router.push('/playground')
    },
    {
        label: '文档中心',
        path: '/document',
        command: () => router.push('/document')
    }
]);

// 判断当前路由是否匹配菜单项
const isActive = path => {
    return route.path.startsWith(path);
};

// 自定义菜单相关
const isMenuVisible = ref(false);
const userAvatarRef = ref(null);
const menuPosition = computed(() => {
    return {
        top: '50px', // 这个值会在toggleUserMenu中根据实际位置动态设置
        right: '0px'
    };
});

// 切换用户菜单显示
const toggleUserMenu = event => {
    event.stopPropagation();
    isMenuVisible.value = !isMenuVisible.value;
};

// 页面导航方法
const navigateTo = path => {
    router.push(path);
    // 关闭菜单
    isMenuVisible.value = false;
};

const navigatorToGitlabIssue = () => {
    window.open(
        'https://git.corpautohome.com/dealer-arch-public/issues/mcp-store-issue/issues',
        '_blank'
    );
};

// 处理退出登录
const handleLogout = () => {
    homeStore.logout();
    // 关闭菜单
    isMenuVisible.value = false;
};

// 点击页面其他地方关闭菜单
const handleClickOutside = event => {
    if (userAvatarRef.value && !userAvatarRef.value.contains(event.target)) {
        isMenuVisible.value = false;
    }
};

const userName = ref('');

// 组件挂载和卸载时添加/移除全局点击事件监听
onMounted(async () => {
    document.addEventListener('click', handleClickOutside);
    userName.value = await homeStore.getUsername();
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
.mcp-top-bar {
    width: 100%;
    height: 70px;
    background-color: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba(226, 232, 240, 1);
    flex-shrink: 0;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 20px;
    cursor: pointer;
}

.logo-img {
    height: 32px;
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: -0.5px;
}

.navigation {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-items {
    display: flex;
    align-items: center;
}

.nav-button {
    margin-right: 8px;
    border-radius: 8px;
    color: #5e6d82;
    font-weight: 500;
    font-size: 15px;

    &:hover {
        background-color: rgba(0, 0, 0, 0.03);
        color: var(--primary-color);
    }

    &.active {
        color: var(--primary-color);
        background-color: rgba(25, 118, 210, 0.08);

        &:deep(.p-button-label) {
            font-weight: bold !important;
        }
    }
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .p-button.p-button-text {
        color: var(--primary-color);
    }
}

.search-component {
    width: 220px;
}

.notification-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 4px;

    &:hover {
        color: var(--primary-color);
    }
}

.user-avatar-wrapper {
    position: relative;
}

.user-avatar {
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--primary-color);
    cursor: pointer;
}

.custom-menu-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    width: 280px;
    background: linear-gradient(to bottom, #ffffff, #fafbfc);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 2px 10px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(0, 0, 0, 0.01);
    z-index: 1000;
    overflow: hidden;
    animation: menuAppear 0.25s cubic-bezier(0.25, 1, 0.5, 1);
    transform-origin: top right;
    backdrop-filter: blur(10px);

    .menu-header {
        padding: 16px 20px;
        display: flex;
        align-items: center;
        gap: 12px;
        background: linear-gradient(
            to right,
            rgba(var(--primary-color-rgb, 25, 118, 210), 0.03),
            rgba(var(--primary-color-rgb, 25, 118, 210), 0.08)
        );
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);

        .menu-avatar {
            background: linear-gradient(135deg, var(--primary-color, #1976d2), #64b5f6);
            color: white;
            box-shadow: 0 3px 8px rgba(var(--primary-color-rgb, 25, 118, 210), 0.2);
            width: 40px;
            height: 40px;
        }

        .user-info {
            display: flex;
            flex-direction: column;

            .user-display-name {
                font-weight: 600;
                font-size: 15px;
                color: #2c3e50;
                margin-bottom: 0;
            }

            .user-role {
                font-size: 13px;
                color: #94a3b8;
                font-weight: 500;
            }
        }
    }

    .menu-items {
        padding: 10px 0;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 0 6px;
        border-radius: 8px;

        &:hover {
            background-color: rgba(var(--primary-color-rgb, 25, 118, 210), 0.06);
        }

        i {
            margin-right: 18px;
            color: var(--primary-color, #1976d2);
            font-size: 16px;
        }

        img {
            width: 20px;
            height: 20px;
            margin-right: 14px;
        }

        span {
            color: #475569;
            font-size: 14px;
            font-weight: 500;
        }
    }

    .logout-item {
        color: #ef4444;

        &:hover {
            background-color: rgba(239, 68, 68, 0.06);
        }

        i {
            color: #ef4444;
        }

        span {
            color: #ef4444;
        }
    }
}

@keyframes menuAppear {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-8px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}
</style>

<style lang="scss">
/* 全局样式 */
.mcp-top-bar .user-actions .p-button {
    font-size: 14px;
}

.mcp-top-bar .notification-btn .p-button {
    background: transparent;
    border: none;
    padding: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background: transparent;
    }

    .p-button-icon {
        color: #666;
        font-size: 20px;
    }
}
</style>
