<template>
    <div class="server-card" @click="goDetail">
        <div class="card-header">
            <div class="server-title">
                <div class="server-title-left">
                    <div class="flex align-items-center">
                        <h3 class="name">{{ client.name }}</h3>
                        <Tag
                            v-if="client.source"
                            :value="getEnumName('SourceType', client.source)"
                            :severity="getClientSourceSeverity(client.source)"
                            class="type-tag"
                        />
                    </div>
                    <p v-if="client.supplier" class="supplier">@{{ client.supplier }}</p>
                </div>
                <p class="server-logo"><img :src="client.logoUrl" alt="logo" /></p>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description">
                <p>{{ client.description || client.introduction }}</p>
            </div>
            <div class="card-meta">
                <span class="meta-item">
                    <i class="pi pi-clock"></i><span>{{ formatDate(client.modifiedStime) }}</span>
                </span>
                <div class="meta-item">
                    <i class="pi pi-dollar"></i>
                    <span>{{ client.priceModel }}</span>
                </div>
            </div>
            <div class="language-section">
                <div class="language">
                    <span v-if="client.supportPlatforms">{{ client.supportPlatforms }}</span>
                </div>
                <div v-if="platformList.length" class="platform-icons">
                    <span v-for="platform in platformList" :key="platform" class="platform-icon">
                        <img :src="getPlatformIcon(platform)" :alt="platform" :title="platform" />
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getEnumName, getSourceSeverity } from '@/enums/sourceType';
import { useRouter } from 'vue-router';
import { computed } from 'vue';
import { getPlatformIcon, parsePlatformList } from '@/enums/oses';
import { formatDate } from '@/utils/commonUtils';

const router = useRouter();

const props = defineProps({
    client: {
        type: Object,
        required: true,
        default: () => ({
            id: '',
            name: '',
            source: '',
            url: '',
            gitUrl: '',
            description: '',
            introduction: '',
            isSupportResources: 0,
            isSupportPrompts: 0,
            isSupportTools: 0,
            isSupportSampling: 0,
            isSupportRoots: 0,
            modifiedStime: '',
            supportOses: '',
            priceModel: '',
            licenseType: '',
            supportPlatforms: '',
        }),
    },
});

// 获取来源对应的样式
const getClientSourceSeverity = (source) => {
    return getSourceSeverity(source, 'client');
};

// 格式化日期
// const formatDate = (dateStr) => {
//     if (!dateStr) {
//         return '';
//     }

//     try {
//         const date = new Date(dateStr);
//         if (isNaN(date.getTime())) return dateStr;

//         return date.toLocaleDateString('zh-CN', {
//             year: 'numeric',
//             month: 'short',
//             day: 'numeric',
//         });
//     } catch (e) {
//         return dateStr;
//     }
// };

// 打开仓库链接
// const openGitUrl = (url) => {
//     window.open(url, '_blank');
// };

// 打开客户端链接
// const openUrl = (url) => {
//     window.open(url, '_blank');
// };

// 跳转到详情页
const goDetail = () => {
    if (props.client && props.client.id) {
        router.push(`/clients/detail/${props.client.id}`);
    }
};

const platformList = computed(() => {
    return parsePlatformList(props.client.supportOses);
});

// const typeList = computed(() => {
//     return parsePlatformList(props.client.supportPlatforms);
// });
</script>
