#!/usr/bin/env node

// 加载环境变量
import dotenv from 'dotenv';
import { resolve } from 'path';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);

// 加载环境变量
dotenv.config({ path: resolve(process.cwd(), '.env.production') });

// 使用 require 导入 CommonJS 模块
const festatic = require('@auto/s3-static-sdk');
const path = require('path');
const fs = require('fs');

// 从环境变量获取配置
const { CDN_ACCESS_KEY_ID, CDN_SECRET_ACCESS_KEY, CDN_PROJECT_NAME } = process.env;

// 检查环境变量是否存在
if (!CDN_ACCESS_KEY_ID || !CDN_SECRET_ACCESS_KEY) {
    console.error(
        '错误: CDN 凭证未设置。请确保 CDN_ACCESS_KEY_ID 和 CDN_SECRET_ACCESS_KEY 环境变量已正确配置。',
    );
    process.exit(1);
}

console.log('开始上传静态资源到CDN...');
console.log('使用的凭证: ', {
    accessKeyId: CDN_ACCESS_KEY_ID.substring(0, 5) + '...',
    secretAccessKey: CDN_SECRET_ACCESS_KEY.substring(0, 5) + '...',
});

// 项目名称和CDN路径前缀
// 尝试从环境变量获取项目名称，如果没有则使用默认值
const projectNameFull = CDN_PROJECT_NAME || 'dealer_microfe_aidev/mcp-store';
const [projectName, cdnPath] = projectNameFull.split('/');
const cdnPrefix = `${cdnPath || 'mcp-store'}/static`;

console.log(`使用项目名称: ${projectName}`);
console.log(`使用CDN路径前缀: ${cdnPrefix}`);

try {
    // 初始化S3客户端
    const s3 = new festatic.S3({
        apiVersion: '2006-03-01', // 固定写法
        accessKeyId: CDN_ACCESS_KEY_ID,
        secretAccessKey: CDN_SECRET_ACCESS_KEY,
        endpoint: 'https://festatic.corpautohome.com/api/endpoint', // 正式环境端点
        s3ForcePathStyle: true, // 固定写法
        sslEnabled: false, // 固定写法
    });

    // 获取构建输出目录
    const distDir = path.resolve(process.cwd(), 'dist');

    // 检查dist目录是否存在
    if (!fs.existsSync(distDir)) {
        console.error(`错误: 构建输出目录 ${distDir} 不存在。请先运行构建命令。`);
        process.exit(1);
    }

    console.log(`上传目录: ${distDir}`);
    console.log(`目标Bucket: ${projectName}`);
    console.log(`CDN路径前缀: ${cdnPrefix}`);

    // 列出可用的存储桶（如果API支持）
    try {
        s3.listBuckets((err, data) => {
            if (err) {
                console.log('无法列出可用的存储桶:', err);
            } else {
                console.log('可用的存储桶:', data.Buckets.map((b) => b.Name).join(', '));
            }
        });
    } catch (error) {
        console.log('列出存储桶时出错:', error);
    }

    // 上传目录
    s3.$uploadDir(
        {
            Dir: distDir,
            Bucket: projectName, // 项目访问路径
            Prefix: cdnPrefix, // CDN路径前缀
            ForceCurrentDir: false, // 是否强制保留当前目录
            Exclude: ['**/*.map'], // 只排除 source map 文件
            Include: ['**/*.{js,css,png,jpg,jpeg,gif,svg,woff,woff2,ttf,eot,html}'], // 包含 HTML 文件
        },
        (err, data) => {
            if (err) {
                console.error('CDN上传失败:', err);

                // 检查是否是项目不存在的错误
                if (
                    err.code === '500' &&
                    err.message &&
                    err.message.includes('project not found')
                ) {
                    console.error(
                        `错误: 项目 "${projectName}" 在CDN服务上不存在。请联系CDN管理员创建此项目，或在环境变量中设置 CDN_PROJECT_NAME 为有效的项目名称。`,
                    );
                    console.error('提示: 您可能需要使用 "autohome" 或其他已存在的项目名称。');
                }

                // 检查是否是凭证错误
                if (err.code === '403' || err.code === 'InvalidAccessKeyId') {
                    console.error('错误: CDN凭证无效或权限不足。请检查您的访问密钥和权限设置。');
                }

                process.exit(1);
                return;
            }

            console.log('CDN上传成功:', data);
            console.log(`静态资源已成功上传到CDN: ${projectName}/${cdnPrefix}`);
            console.log(
                `您可以通过以下URL访问您的应用: https://z.autoimg.cn/${projectName}/${cdnPrefix}/index.html`,
            );
        },
    );
} catch (error) {
    console.error('CDN上传过程中发生错误:', error);
    process.exit(1);
}
