{"name": "mcp-store", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production && node scripts/upload-to-cdn.js", "upload:cdn": "node scripts/upload-to-cdn.js", "preview": "vite preview", "serve:dev": "vite --mode development", "serve:test": "vite --mode test", "serve:prod": "vite --mode production", "lint:fix-all": "eslint \"src/**/*.{vue,js}\" --fix --quiet", "prepare": "husky"}, "dependencies": {"@auto/s3-static-sdk": "^2.0.9", "@kangc/v-md-editor": "^2.3.18", "@vue/eslint-config-prettier": "^10.2.0", "@vueuse/core": "^12.5.0", "dayjs": "^1.11.13", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "i18n-jsautotranslate": "^3.15.1", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "pinia": "^2.2.2", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primevue": "^3.52", "swiper": "10.3.1", "vconsole": "^3.15.1", "vue": "^3.4.37", "vue-router": "^4.4.3"}, "lint-staged": {"*.{js,vue}": ["eslint --fix"]}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/preset-env": "^7.26.0", "@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^5.1.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/eslint-config-prettier": "^7.0.0", "code-inspector-plugin": "^0.15.2", "dotenv": "^16.4.7", "eslint": "^7.32.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "^7.20.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "2.8.8", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.77.8", "terser": "^5.39.0", "vite": "^5.4.1", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^10.1.3"}, "packageManager": "pnpm@8.15.4"}